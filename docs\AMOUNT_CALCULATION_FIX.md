# 商场客户月度账单金额计算修复说明

## 🎯 修复目标

修复商场客户月度账单功能中关联订单的金额计算错误问题，确保显示的金额与订单创建时的价格信息完全一致。

## 🐛 问题描述

### 原始问题
在商场客户管理页面 → 月度账单 → 查看订单模态框中，订单列表表格显示的金额计算不正确：

1. **原始金额错误**：当前显示的不是商品的真实原价
2. **折扣金额错误**：当前计算的折扣金额与实际折扣不符

### 业务逻辑要求
- **原始金额** = 商品的基础价格（未打折前的价格）
- **实际金额** = 商品的折后价格（客户实际支付的价格）  
- **折扣金额** = 原始金额 - 实际金额

### 参考示例
订单提交过程中显示：原价 ¥15.00 → 折后 ¥9.00 (单品6.0折)
- 原始金额应显示：¥15.00
- 实际金额应显示：¥9.00  
- 折扣金额应显示：¥6.00

## 🔧 修复方案

### 1. 后端API修复 (`app.py`)

#### 修复位置：`/api/bills/<bill_id>/orders` 接口

**修复前问题：**
- 没有正确计算订单的原始总金额
- 返回的金额数据结构不完整
- 折扣金额计算逻辑错误

**修复后改进：**
```python
# 正确计算每个衣物项目的金额
for clothing in order.clothes:
    # 优先使用original_price，如果为空或0则使用price
    if clothing.original_price and clothing.original_price > 0:
        original_price = clothing.original_price
    else:
        original_price = clothing.price
    
    quantity = clothing.quantity or 1
    actual_price = clothing.price
    
    # 计算单项金额
    item_original_amount = original_price * quantity
    item_actual_amount = actual_price * quantity
    
    # 累加到订单总金额
    order_original_amount += item_original_amount
    order_actual_amount += item_actual_amount

# 计算订单折扣金额
order_discount_amount = order_original_amount - order_actual_amount
```

**返回数据结构：**
```json
{
  "id": 123,
  "order_number": "ORD001",
  "original_amount": 15.00,    // 原始总金额
  "total_amount": 9.00,        // 实际总金额（折扣后）
  "discount_amount": 6.00,     // 折扣金额
  "clothing_items": [
    {
      "name": "衬衫",
      "original_price": 15.00,  // 原始单价
      "price": 9.00,            // 实际单价
      "quantity": 1,
      "item_original_amount": 15.00,  // 单项原始金额
      "item_actual_amount": 9.00      // 单项实际金额
    }
  ]
}
```

### 2. 前端JavaScript修复 (`templates/mall_customer_management.html`)

#### 修复位置：订单列表显示逻辑

**修复前问题：**
- 前端重复计算原始金额，逻辑复杂且容易出错
- 没有使用后端返回的正确金额数据

**修复后改进：**
```javascript
// 直接使用后端计算好的正确金额数据
const originalAmount = order.original_amount || 0;  // 原始总金额
const actualAmount = order.total_amount || 0;       // 实际总金额
const discountAmount = order.discount_amount || 0;  // 折扣金额

// 显示在表格中
const row = `
    <tr>
        <td>${order.order_number}</td>
        <td>${originalAmount.toFixed(2)} 元</td>
        <td>${actualAmount.toFixed(2)} 元</td>
        <td>${discountAmount.toFixed(2)} 元</td>
    </tr>
`;
```

#### 修复位置：衣物详情显示逻辑

**修复后改进：**
```javascript
// 正确显示折扣信息
let discountDisplay = '无折扣';
if (originalPrice > actualPrice && originalPrice > 0) {
    const discountPercent = (actualPrice / originalPrice * 10).toFixed(1);
    discountDisplay = `${discountPercent} 折`;
}
```

### 3. Excel导出功能修复

#### 修复位置：`/api/bills/<bill_id>/export` 接口

**修复内容：**
- 统一金额计算逻辑，与API接口保持一致
- 正确计算订单原始总额和实际总额
- 修复Excel中的金额显示

## ✅ 修复效果验证

### 测试脚本
运行测试脚本验证修复效果：
```bash
python test_amount_calculation_fix.py
```

### 验证要点

1. **金额计算逻辑验证**
   - 原始金额 - 折扣金额 = 实际金额
   - 单项金额累加 = 订单总金额

2. **API数据验证**
   - 返回数据包含所有必要字段
   - 金额数据类型和精度正确

3. **业务场景验证**
   - 原价 ¥15.00 → 折后 ¥9.00 (6.0折)
   - 折扣金额 = ¥6.00

### 预期结果

修复后，账单订单列表中显示的金额应该：
- ✅ 原始金额正确显示商品基础价格
- ✅ 实际金额正确显示折扣后价格
- ✅ 折扣金额 = 原始金额 - 实际金额
- ✅ 与订单创建时的价格信息完全一致

## 🚀 使用说明

### 功能验证步骤

1. **访问商场客户管理页面**
   - 选择一个有月度账单的商场客户
   - 点击"查看详情"

2. **查看月度账单**
   - 在账单列表中点击"查看订单"
   - 检查订单列表中的金额显示

3. **验证金额计算**
   - 原始金额列：显示商品原价总和
   - 实际金额列：显示折扣后价格总和
   - 折扣金额列：显示折扣总金额

4. **查看衣物详情**
   - 点击"查看衣物"按钮
   - 检查每件衣物的价格和折扣信息

5. **Excel导出验证**
   - 点击"导出Excel"按钮
   - 检查导出文件中的金额数据

## 📋 技术细节

### 数据库字段说明
- `clothing.original_price`: 商品原始价格（折扣前）
- `clothing.price`: 商品实际价格（折扣后）
- `clothing.quantity`: 商品数量
- `clothing.discount_rate`: 折扣率

### 计算公式
```
单项原始金额 = original_price × quantity
单项实际金额 = price × quantity
单项折扣金额 = 单项原始金额 - 单项实际金额

订单原始总金额 = Σ(单项原始金额)
订单实际总金额 = Σ(单项实际金额)
订单折扣总金额 = 订单原始总金额 - 订单实际总金额
```

### 兼容性处理
- 如果 `original_price` 为空或0，使用 `price` 作为原始价格
- 如果 `quantity` 为空，默认为1
- 如果没有衣物记录，使用订单的 `total_amount`

## 🎉 修复完成

此次修复确保了商场客户月度账单功能中金额计算的准确性和一致性，解决了原始金额和折扣金额显示错误的问题。所有金额计算都基于正确的业务逻辑，与订单创建时的价格信息完全一致。
