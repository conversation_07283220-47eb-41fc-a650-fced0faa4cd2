// 主要JS功能文件
document.addEventListener('DOMContentLoaded', function() {
    // 客户搜索功能
    const searchCustomerBtn = document.getElementById('searchCustomer');
    if (searchCustomerBtn) {
        searchCustomerBtn.addEventListener('click', function() {
            const phoneNumber = document.getElementById('phoneNumber').value;
            if (!phoneNumber) {
                alert('请输入手机号码');
                return;
            }
            
            // 模拟API调用获取客户信息
            // 实际应用中应该替换为真实的API调用
            fetchCustomerData(phoneNumber);
        });
    }
    
    // 余额支付功能
    const paymentMethodRadios = document.querySelectorAll('input[name="paymentMethod"]');
    if (paymentMethodRadios.length > 0) {
        paymentMethodRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                updatePaymentUI(this.value);
            });
        });
    }
    
    // 初始化充值模态框事件监听
    initRechargeModal();
});

// 获取客户数据
function fetchCustomerData(phoneNumber) {
    // 模拟API调用，实际使用时应替换为真实的AJAX请求
    setTimeout(() => {
        // 模拟客户数据
        const customerData = {
            name: '张三',
            phone: phoneNumber,
            balance: 150.00,
            id: 12345
        };
        
        displayCustomerInfo(customerData);
    }, 500);
}

// 显示客户信息
function displayCustomerInfo(customer) {
    const customerInfoDiv = document.getElementById('customerInfo');
    if (customerInfoDiv) {
        customerInfoDiv.innerHTML = `
            <div class="alert alert-info">
                <h5>客户信息</h5>
                <p><strong>姓名:</strong> ${customer.name}</p>
                <p><strong>手机号:</strong> ${customer.phone}</p>
                <p><strong>当前余额:</strong> <span id="customerBalance">${customer.balance.toFixed(2)}</span> 元</p>
                <input type="hidden" id="customerId" value="${customer.id}">
            </div>
        `;
        
        // 显示余额支付选项
        const balancePaymentOption = document.getElementById('balancePaymentOption');
        if (balancePaymentOption) {
            balancePaymentOption.style.display = 'block';
            
            // 更新标签文本显示可用余额
            const balanceLabel = document.querySelector('label[for="balancePayment"]');
            if (balanceLabel) {
                balanceLabel.innerHTML = `余额支付 <span class="text-info">(可用: ${customer.balance.toFixed(2)}元)</span>`;
            }
        }
    }
}

// 根据支付方式更新UI
function updatePaymentUI(paymentMethod) {
    const totalAmount = calculateTotalAmount();
    const customerBalance = document.getElementById('customerBalance') 
        ? parseFloat(document.getElementById('customerBalance').textContent) 
        : 0;
        
    if (paymentMethod === 'balance') {
        // 检查余额是否足够
        if (customerBalance < totalAmount) {
            alert(`余额不足！当前余额: ${customerBalance.toFixed(2)}元，需支付: ${totalAmount.toFixed(2)}元`);
            // 自动切换到现金支付
            document.getElementById('cashPayment').checked = true;
            // 显示充值按钮
            showRechargeButton();
            return;
        }
    }
    
    // 更新支付表单的相关元素
    updatePaymentForm(paymentMethod);
}

// 计算总金额
function calculateTotalAmount() {
    // 获取所有已选中的衣物及其数量
    const orderItems = document.querySelectorAll('.order-item');
    let total = 0;
    
    orderItems.forEach(item => {
        const price = parseFloat(item.dataset.price || 0);
        const quantity = parseInt(item.querySelector('.item-quantity').value || 0);
        total += price * quantity;
    });
    
    return total;
}

// 更新支付表单
function updatePaymentForm(paymentMethod) {
    const cashDetailsDiv = document.getElementById('cashPaymentDetails');
    const cardDetailsDiv = document.getElementById('cardPaymentDetails');
    const balanceDetailsDiv = document.getElementById('balancePaymentDetails');
    
    // 隐藏所有支付详情
    if (cashDetailsDiv) cashDetailsDiv.style.display = 'none';
    if (cardDetailsDiv) cardDetailsDiv.style.display = 'none';
    if (balanceDetailsDiv) balanceDetailsDiv.style.display = 'none';
    
    // 显示选中的支付方式详情
    switch (paymentMethod) {
        case 'cash':
            if (cashDetailsDiv) cashDetailsDiv.style.display = 'block';
            break;
        case 'card':
            if (cardDetailsDiv) cardDetailsDiv.style.display = 'block';
            break;
        case 'balance':
            if (balanceDetailsDiv) balanceDetailsDiv.style.display = 'block';
            break;
    }
}

// 显示充值按钮
function showRechargeButton() {
    const rechargeButtonDiv = document.getElementById('rechargeButtonContainer');
    if (rechargeButtonDiv) {
        rechargeButtonDiv.innerHTML = `
            <button type="button" class="btn btn-warning mb-3" id="openRechargeModal">
                充值余额 <i class="fas fa-wallet"></i>
            </button>
        `;
        
        // 添加充值按钮点击事件
        document.getElementById('openRechargeModal').addEventListener('click', function() {
            openRechargeModal();
        });
    }
}

// 初始化充值模态框
function initRechargeModal() {
    // 检查模态框是否已存在
    let rechargeModal = document.getElementById('rechargeModal');
    
    // 如果模态框不存在，创建它
    if (!rechargeModal) {
        const modalHTML = `
            <div class="modal fade" id="rechargeModal" tabindex="-1" aria-labelledby="rechargeModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="rechargeModalLabel">账户充值</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <h6>请选择充值金额：</h6>
                                <div class="d-flex flex-wrap gap-2 mb-3">
                                    <button type="button" class="btn btn-outline-primary recharge-amount" data-amount="500">500元</button>
                                    <button type="button" class="btn btn-outline-primary recharge-amount" data-amount="1000">1000元</button>
                                    <button type="button" class="btn btn-outline-primary recharge-amount" data-amount="3000">3000元</button>
                                    <button type="button" class="btn btn-outline-primary recharge-amount" data-amount="10000">10000元</button>
                                </div>
                                <div class="input-group">
                                    <span class="input-group-text">自定义金额</span>
                                    <input type="number" class="form-control" id="customRechargeAmount" min="1" step="1" placeholder="输入充值金额">
                                    <button class="btn btn-outline-secondary" type="button" id="setCustomAmount">确定</button>
                                </div>
                            </div>
                            <div class="mb-3">
                                <h6>选择支付方式：</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="rechargePaymentMethod" id="rechargeCash" value="cash" checked>
                                    <label class="form-check-label" for="rechargeCash">现金支付</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="rechargePaymentMethod" id="rechargeCard" value="card">
                                    <label class="form-check-label" for="rechargeCard">银行卡支付</label>
                                </div>
                            </div>
                            <div class="alert alert-primary">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>充值金额:</span>
                                    <span id="selectedRechargeAmount" class="h5 mb-0">¥0.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmRecharge" disabled>确认充值</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 将模态框添加到文档中
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // 获取模态框元素
        rechargeModal = document.getElementById('rechargeModal');
    }
    
    // 添加事件监听
    addRechargeModalEventListeners(rechargeModal);
}

// 添加充值模态框事件监听
function addRechargeModalEventListeners(modal) {
    if (!modal) return;
    
    // 选择预设金额
    const amountButtons = modal.querySelectorAll('.recharge-amount');
    amountButtons.forEach(button => {
        button.addEventListener('click', function() {
            const amount = parseFloat(this.dataset.amount);
            selectRechargeAmount(amount);
            
            // 移除其他按钮的选中状态
            amountButtons.forEach(btn => btn.classList.remove('active', 'btn-primary'));
            btn.classList.remove('btn-outline-primary');
            
            // 添加选中状态
            this.classList.add('active', 'btn-primary');
        });
    });
    
    // 自定义金额
    const customAmountBtn = modal.querySelector('#setCustomAmount');
    if (customAmountBtn) {
        customAmountBtn.addEventListener('click', function() {
            const customAmount = parseFloat(document.getElementById('customRechargeAmount').value);
            if (isNaN(customAmount) || customAmount <= 0) {
                alert('请输入有效的充值金额');
                return;
            }
            
            // 移除预设按钮的选中状态
            amountButtons.forEach(btn => {
                btn.classList.remove('active', 'btn-primary');
                btn.classList.add('btn-outline-primary');
            });
            
            // 设置自定义金额
            selectRechargeAmount(customAmount);
        });
    }
    
    // 确认充值
    const confirmButton = modal.querySelector('#confirmRecharge');
    if (confirmButton) {
        confirmButton.addEventListener('click', function() {
            const amount = parseFloat(document.getElementById('selectedRechargeAmount').textContent.replace('¥', ''));
            const paymentMethod = document.querySelector('input[name="rechargePaymentMethod"]:checked').value;
            
            confirmRecharge(amount, paymentMethod);
        });
    }
}

// 打开充值模态框
function openRechargeModal() {
    const modal = new bootstrap.Modal(document.getElementById('rechargeModal'));
    modal.show();
    
    // 重置模态框状态
    resetRechargeModal();
}

// 重置充值模态框状态
function resetRechargeModal() {
    const amountButtons = document.querySelectorAll('.recharge-amount');
    amountButtons.forEach(btn => {
        btn.classList.remove('active', 'btn-primary');
        btn.classList.add('btn-outline-primary');
    });
    
    document.getElementById('customRechargeAmount').value = '';
    document.getElementById('selectedRechargeAmount').textContent = '¥0.00';
    document.getElementById('confirmRecharge').disabled = true;
    document.getElementById('rechargeCash').checked = true;
}

// 选择充值金额
function selectRechargeAmount(amount) {
    const amountDisplay = document.getElementById('selectedRechargeAmount');
    const confirmButton = document.getElementById('confirmRecharge');
    
    if (amountDisplay) {
        amountDisplay.textContent = `¥${amount.toFixed(2)}`;
    }
    
    if (confirmButton) {
        confirmButton.disabled = amount <= 0;
    }
}

// 确认充值
function confirmRecharge(amount, paymentMethod) {
    const customerId = document.getElementById('customerId').value;
    
    // 模拟充值处理
    // 在实际应用中，这里应该是一个API调用
    setTimeout(() => {
        // 获取当前余额
        const balanceElement = document.getElementById('customerBalance');
        if (balanceElement) {
            const currentBalance = parseFloat(balanceElement.textContent);
            const newBalance = currentBalance + amount;
            
            // 更新显示的余额
            balanceElement.textContent = newBalance.toFixed(2);
            
            // 更新余额支付选项标签
            const balanceLabel = document.querySelector('label[for="balancePayment"]');
            if (balanceLabel) {
                balanceLabel.innerHTML = `余额支付 <span class="text-info">(可用: ${newBalance.toFixed(2)}元)</span>`;
            }
            
            // 如果当前选择的是余额支付，重新检查余额是否足够
            const balancePaymentRadio = document.getElementById('balancePayment');
            if (balancePaymentRadio && balancePaymentRadio.checked) {
                updatePaymentUI('balance');
            }
        }
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('rechargeModal'));
        if (modal) {
            modal.hide();
        }
        
        // 显示成功消息
        alert(`充值成功！已为客户(ID: ${customerId})充值${amount.toFixed(2)}元。`);
    }, 1000);
} 