#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库更新脚本 - 添加整体折扣率字段
"""

import sqlite3
import os
from datetime import datetime

def update_database():
    """更新数据库，添加整体折扣率字段"""
    db_path = os.path.join('instance', 'laundry.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(mall_customer)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'overall_discount_rate' not in columns:
            print("添加 overall_discount_rate 字段到 mall_customer 表...")
            cursor.execute("""
                ALTER TABLE mall_customer 
                ADD COLUMN overall_discount_rate REAL DEFAULT 1.0
            """)
            print("✓ overall_discount_rate 字段添加成功")
        else:
            print("overall_discount_rate 字段已存在，跳过添加")
        
        # 提交更改
        conn.commit()
        print("数据库更新完成！")
        
        return True
        
    except Exception as e:
        print(f"数据库更新失败: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    print("开始更新数据库...")
    success = update_database()
    if success:
        print("数据库更新成功完成！")
    else:
        print("数据库更新失败！")
