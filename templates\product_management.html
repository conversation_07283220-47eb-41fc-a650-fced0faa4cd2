<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soulweave改衣坊 - 商品管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .header {
            background-color: #343a40;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header-controls {
            display: flex;
            align-items: center;
        }

        .staff-info {
            margin-right: 20px;
            font-size: 14px;
        }

        .logout-btn {
            color: #f8f9fa;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: #dc3545;
            font-size: 14px;
        }

        .back-btn {
            color: #f8f9fa;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: #007bff;
            font-size: 14px;
            margin-right: 10px;
        }

        .container {
            padding: 20px;
            max-width: 1200px;
        }

        .control-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .search-box {
            display: flex;
            align-items: center;
        }

        .search-input {
            margin-right: 10px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .filter-dropdown {
            margin-right: 10px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .search-btn {
            padding: 5px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .add-btn {
            padding: 5px 15px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .product-table {
            width: 100%;
            border-collapse: collapse;
        }

        .product-table th, .product-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }

        .product-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .product-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .product-actions {
            display: flex;
            gap: 5px;
        }

        .edit-btn {
            padding: 3px 8px;
            background-color: #ffc107;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .delete-btn {
            padding: 3px 8px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination-btn {
            padding: 5px 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            margin: 0 5px;
            cursor: pointer;
        }

        .pagination-btn.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .required-field::after {
            content: " *";
            color: red;
        }

        .status-active {
            color: #28a745;
            font-weight: bold;
        }

        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Soulweave改衣坊管理系统</h1>
        <div class="header-controls">
            <div class="staff-info">
                当前用户: <span id="staffName">{{ session.staff_name }}</span>
                <span class="mx-2">|</span>
                角色: <span id="staffRole">{{ session.staff_role }}</span>
            </div>
            <a href="/" class="back-btn">返回首页</a>
            <a href="/logout" class="logout-btn">登出</a>
        </div>
    </div>

    <div class="container">
        <h2>商品管理</h2>

        <div class="control-bar">
            <div class="search-box">
                <input type="text" id="searchInput" class="search-input" placeholder="搜索商品名称">
                <select id="categoryFilter" class="filter-dropdown">
                    <option value="">所有分类</option>
                    <option value="上衣">上衣</option>
                    <option value="裤装">裤装</option>
                    <option value="外套">外套</option>
                    <option value="裙装">裙装</option>
                    <option value="套装">套装</option>
                    <option value="其他">其他</option>
                </select>
                <select id="statusFilter" class="filter-dropdown">
                    <option value="">所有状态</option>
                    <option value="true">启用</option>
                    <option value="false">禁用</option>
                </select>
                <button id="searchBtn" class="search-btn">搜索</button>
            </div>
            <button id="addProductBtn" class="add-btn">添加商品</button>
        </div>

        <table class="product-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>商品名称</th>
                    <th>分类</th>
                    <th>洗衣价格(元)</th>
                    <th>织补价格(元)</th>
                    <th>改衣价格(元)</th>
                    <th>其他价格(元)</th>
                    <th>描述</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="productTableBody">
                <!-- 商品数据将通过JavaScript动态加载 -->
            </tbody>
        </table>

        <div class="pagination" id="pagination">
            <!-- 分页控件将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 添加/编辑商品模态框 -->
    <div class="modal fade" id="productModal" tabindex="-1" role="dialog" aria-labelledby="productModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productModalLabel">添加商品</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="productForm">
                        <input type="hidden" id="productId">
                        <div class="form-group">
                            <label for="productName" class="required-field">商品名称</label>
                            <input type="text" id="productName" required>
                        </div>
                        <div class="form-group">
                            <label for="productCategory" class="required-field">商品分类</label>
                            <select id="productCategory" required>
                                <option value="">请选择分类</option>
                                <option value="上衣">上衣</option>
                                <option value="裤装">裤装</option>
                                <option value="外套">外套</option>
                                <option value="裙装">裙装</option>
                                <option value="套装">套装</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <!-- 基础价格字段已移除，只保留服务类型价格 -->

                        <!-- 服务价格设置 -->
                        <div class="form-group">
                            <h6 style="margin-bottom: 10px; color: #495057; border-bottom: 1px solid #dee2e6; padding-bottom: 5px;">服务价格设置</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="washPrice" class="required-field">洗衣价格(元)</label>
                                    <input type="number" id="washPrice" min="0" step="0.01" value="15" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="mendPrice" class="required-field">织补价格(元)</label>
                                    <input type="number" id="mendPrice" min="0" step="0.01" value="20" required>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="alterPrice" class="required-field">改衣价格(元)</label>
                                    <input type="number" id="alterPrice" min="0" step="0.01" value="30" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="otherPrice" class="required-field">其他价格(元)</label>
                                    <input type="number" id="otherPrice" min="0" step="0.01" value="20" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="productDescription">描述</label>
                            <textarea id="productDescription" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="productStatus">状态</label>
                            <select id="productStatus">
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveProductBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>确定要删除商品 "<span id="deleteProductName"></span>" 吗？</p>
                    <p class="text-danger">注意：此操作不可逆！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript库 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>

    <script>
        // 当前页码和商品ID
        let currentPage = 1;
        let deleteProductId = null;

        // 页面加载时获取商品列表
        $(document).ready(function() {
            loadProducts();

            // 添加事件监听器
            $("#searchBtn").click(loadProducts);
            $("#addProductBtn").click(showAddProductModal);
            $("#saveProductBtn").click(saveProduct);
            $("#confirmDeleteBtn").click(deleteProduct);

            // 回车键搜索
            $("#searchInput").keypress(function(e) {
                if (e.which === 13) {
                    loadProducts();
                }
            });
        });

        // 加载商品列表
        function loadProducts() {
            const search = $("#searchInput").val();
            const category = $("#categoryFilter").val();
            const status = $("#statusFilter").val();

            $.ajax({
                url: "/api/products",
                method: "GET",
                data: {
                    search: search,
                    category: category,
                    is_active: status,
                    page: currentPage,
                    per_page: 10
                },
                success: function(response) {
                    renderProducts(response.products);
                    renderPagination(response);
                },
                error: function(xhr) {
                    alert("获取商品列表失败，请重试");
                    console.error(xhr.responseText);
                }
            });
        }

        // 渲染商品列表
        function renderProducts(products) {
            const tableBody = $("#productTableBody");
            tableBody.empty();

            if (products.length === 0) {
                tableBody.append('<tr><td colspan="10" class="text-center">没有找到商品</td></tr>');
                return;
            }

            products.forEach(product => {
                const row = `
                    <tr>
                        <td>${product.id}</td>
                        <td>${product.name}</td>
                        <td>${product.category}</td>
                        <td>${product.wash_price ? product.wash_price.toFixed(2) : '15.00'}</td>
                        <td>${product.mend_price ? product.mend_price.toFixed(2) : '20.00'}</td>
                        <td>${product.alter_price ? product.alter_price.toFixed(2) : '30.00'}</td>
                        <td>${product.other_price ? product.other_price.toFixed(2) : '20.00'}</td>
                        <td>${product.description || '-'}</td>
                        <td class="${product.is_active ? 'status-active' : 'status-inactive'}">${product.is_active ? '启用' : '禁用'}</td>
                        <td class="product-actions">
                            <button class="btn btn-sm btn-warning" onclick="showEditProductModal(${product.id})">编辑</button>
                            <button class="btn btn-sm btn-danger" onclick="showDeleteModal(${product.id}, '${product.name}')">删除</button>
                        </td>
                    </tr>
                `;
                tableBody.append(row);
            });
        }

        // 渲染分页控件
        function renderPagination(response) {
            const pagination = $("#pagination");
            pagination.empty();

            const totalPages = response.total_pages;
            if (totalPages <= 1) return;

            // 上一页按钮
            pagination.append(`
                <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="goToPage(${currentPage - 1})">
                    &laquo;
                </button>
            `);

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                pagination.append(`
                    <button class="pagination-btn ${currentPage === i ? 'active' : ''}" onclick="goToPage(${i})">
                        ${i}
                    </button>
                `);
            }

            // 下一页按钮
            pagination.append(`
                <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="goToPage(${currentPage + 1})">
                    &raquo;
                </button>
            `);
        }

        // 跳转到指定页
        function goToPage(page) {
            currentPage = page;
            loadProducts();
        }

        // 显示添加商品模态框
        function showAddProductModal() {
            // 重置表单
            $("#productForm")[0].reset();
            $("#productId").val("");

            // 设置默认值
            $("#productStatus").val("true");

            // 更新标题
            $("#productModalLabel").text("添加商品");

            // 显示模态框
            $("#productModal").modal("show");
        }

        // 显示编辑商品模态框
        function showEditProductModal(productId) {
            // 重置表单
            $("#productForm")[0].reset();

            // 获取商品详情
            $.ajax({
                url: `/api/products/${productId}`,
                method: "GET",
                success: function(product) {
                    $("#productId").val(product.id);
                    $("#productName").val(product.name);
                    $("#productCategory").val(product.category);
                    // 基础价格字段已移除，不再设置
                    $("#washPrice").val(product.wash_price || 15);
                    $("#mendPrice").val(product.mend_price || 20);
                    $("#alterPrice").val(product.alter_price || 30);
                    $("#otherPrice").val(product.other_price || 20);
                    $("#productDescription").val(product.description);
                    $("#productStatus").val(product.is_active.toString());

                    // 更新标题
                    $("#productModalLabel").text("编辑商品");

                    // 显示模态框
                    $("#productModal").modal("show");
                },
                error: function(xhr) {
                    alert("获取商品详情失败，请重试");
                    console.error(xhr.responseText);
                }
            });
        }

        // 保存商品
        function saveProduct() {
            const productId = $("#productId").val();

            const productData = {
                name: $("#productName").val(),
                category: $("#productCategory").val(),
                // 基础价格字段已移除，不再发送
                wash_price: parseFloat($("#washPrice").val()),
                mend_price: parseFloat($("#mendPrice").val()),
                alter_price: parseFloat($("#alterPrice").val()),
                other_price: parseFloat($("#otherPrice").val()),
                description: $("#productDescription").val(),
                is_active: $("#productStatus").val() === "true"
            };

            // 验证必填字段
            if (!productData.name || !productData.category) {
                alert("请填写商品名称和分类");
                return;
            }

            // 验证服务价格
            if (isNaN(productData.wash_price) || isNaN(productData.mend_price) ||
                isNaN(productData.alter_price) || isNaN(productData.other_price)) {
                alert("请填写所有服务价格");
                return;
            }

            // 验证价格不能为负数
            if (productData.wash_price < 0 || productData.mend_price < 0 ||
                productData.alter_price < 0 || productData.other_price < 0) {
                alert("服务价格不能为负数");
                return;
            }

            const url = productId ? `/api/products/${productId}` : "/api/products";
            const method = productId ? "PUT" : "POST";

            $.ajax({
                url: url,
                method: method,
                contentType: "application/json",
                data: JSON.stringify(productData),
                success: function(response) {
                    $("#productModal").modal("hide");
                    loadProducts();
                    alert(productId ? "商品更新成功" : "商品添加成功");
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : "操作失败，请重试";
                    alert(errorMsg);
                    console.error(xhr.responseText);
                }
            });
        }

        // 显示删除确认模态框
        function showDeleteModal(productId, productName) {
            deleteProductId = productId;
            $("#deleteProductName").text(productName);
            $("#deleteModal").modal("show");
        }

        // 删除商品
        function deleteProduct() {
            if (!deleteProductId) return;

            $.ajax({
                url: `/api/products/${deleteProductId}`,
                method: "DELETE",
                success: function(response) {
                    $("#deleteModal").modal("hide");
                    loadProducts();
                    alert("商品删除成功");
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : "删除失败，请重试";
                    alert(errorMsg);
                    console.error(xhr.responseText);
                }
            });
        }
    </script>
</body>
</html>
