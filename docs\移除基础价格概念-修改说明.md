# 移除基础价格概念 - 修改说明

## 修改概述

本次修改完全移除了系统中的"基础价格"概念，只保留服务类型价格（洗衣、织补、改衣、其他），确保价格计算逻辑的一致性和简洁性。

## 🎯 修改目标

1. **完全移除基础价格概念**：从数据库模型、API接口、前端界面中移除基础价格相关功能
2. **保持数据库兼容性**：保留price字段但不再使用，确保现有数据不受影响
3. **统一价格计算逻辑**：总价格 = 选中服务类型价格之和
4. **保持历史数据兼容**：确保现有订单数据正常显示

## 📋 修改内容

### 1. 数据库模型修改

**文件**: `models.py`

```python
class Product(db.Model):
    # price字段已废弃，保留用于数据库兼容性，但不再使用
    price = db.Column(db.Float, default=0.0, nullable=True)  # 已废弃的基础价格字段
    
    # 服务类型价格字段（保持不变）
    wash_price = db.Column(db.Float, default=15.0, nullable=False)  # 洗衣价格
    mend_price = db.Column(db.Float, default=20.0, nullable=False)  # 织补价格
    alter_price = db.Column(db.Float, default=30.0, nullable=False)  # 改衣价格
    other_price = db.Column(db.Float, default=20.0, nullable=False)  # 其他服务价格

    def to_dict(self):
        """返回商品字典，不包含已废弃的基础价格字段"""
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'description': self.description,
            'is_active': self.is_active,
            'wash_price': self.wash_price,
            'mend_price': self.mend_price,
            'alter_price': self.alter_price,
            'other_price': self.other_price
        }
```

**修改说明**：
- 将price字段标记为已废弃，但保留用于数据库兼容性
- 修改to_dict()方法，不再返回基础价格字段
- 添加详细注释说明字段状态

### 2. API接口修改

**文件**: `app.py`

#### 商品列表API
```python
# 准备返回数据（不包含已废弃的基础价格）
product_list = []
for product in products:
    product_list.append({
        'id': product.id,
        'name': product.name,
        'category': product.category,
        'wash_price': product.wash_price,
        'mend_price': product.mend_price,
        'alter_price': product.alter_price,
        'other_price': product.other_price
    })
```

#### 商品创建API
```python
# 创建新商品（不包含已废弃的基础价格）
new_product = Product(
    name=data['name'],
    category=data['category'],
    price=0.0,  # 基础价格字段设为0，保持数据库兼容性
    wash_price=float(data.get('wash_price', 15.0)),
    mend_price=float(data.get('mend_price', 20.0)),
    alter_price=float(data.get('alter_price', 30.0)),
    other_price=float(data.get('other_price', 20.0)),
    description=data.get('description', ''),
    is_active=data.get('is_active', True)
)
```

#### 商品更新API
```python
# 更新商品信息（不包含已废弃的基础价格）
product.name = data['name']
product.category = data['category']
# 基础价格字段保持不变，不再更新
product.wash_price = float(data.get('wash_price', product.wash_price))
product.mend_price = float(data.get('mend_price', product.mend_price))
product.alter_price = float(data.get('alter_price', product.alter_price))
product.other_price = float(data.get('other_price', product.other_price))
```

### 3. 商品管理页面修改

**文件**: `templates/product_management.html`

#### 表格列修改
```html
<thead>
    <tr>
        <th>ID</th>
        <th>商品名称</th>
        <th>分类</th>
        <!-- 移除了基础价格列 -->
        <th>洗衣价格(元)</th>
        <th>织补价格(元)</th>
        <th>改衣价格(元)</th>
        <th>其他价格(元)</th>
        <th>描述</th>
        <th>状态</th>
        <th>操作</th>
    </tr>
</thead>
```

#### 模态框修改
```html
<!-- 基础价格字段已移除，只保留服务类型价格 -->
<div class="form-group">
    <h6>服务价格设置</h6>
    <div class="row">
        <div class="col-md-6">
            <label for="washPrice" class="required-field">洗衣价格(元)</label>
            <input type="number" id="washPrice" min="0" step="0.01" value="15" required>
        </div>
        <!-- 其他服务价格输入框... -->
    </div>
</div>
```

#### JavaScript修改
```javascript
// 渲染商品列表（移除基础价格列）
const row = `
    <tr>
        <td>${product.id}</td>
        <td>${product.name}</td>
        <td>${product.category}</td>
        <td>${product.wash_price ? product.wash_price.toFixed(2) : '15.00'}</td>
        <td>${product.mend_price ? product.mend_price.toFixed(2) : '20.00'}</td>
        <td>${product.alter_price ? product.alter_price.toFixed(2) : '30.00'}</td>
        <td>${product.other_price ? product.other_price.toFixed(2) : '20.00'}</td>
        <td>${product.description || '-'}</td>
        <td class="${product.is_active ? 'status-active' : 'status-inactive'}">${product.is_active ? '启用' : '禁用'}</td>
        <td class="product-actions">
            <button class="btn btn-sm btn-warning" onclick="showEditProductModal(${product.id})">编辑</button>
            <button class="btn btn-sm btn-danger" onclick="showDeleteModal(${product.id}, '${product.name}')">删除</button>
        </td>
    </tr>
`;

// 保存商品数据（移除基础价格字段）
const productData = {
    name: $("#productName").val(),
    category: $("#productCategory").val(),
    // 基础价格字段已移除，不再发送
    wash_price: parseFloat($("#washPrice").val()),
    mend_price: parseFloat($("#mendPrice").val()),
    alter_price: parseFloat($("#alterPrice").val()),
    other_price: parseFloat($("#otherPrice").val()),
    description: $("#productDescription").val(),
    is_active: $("#productStatus").val() === "true"
};
```

### 4. 静态配置文件修改

**文件**: `static/js/clothing-options.js`

#### 衣物选项修改
```javascript
// 全局衣物选项（已移除基础价格，只保留分类和名称）
window.clothingOptions = [
    {
        category: '上衣',
        items: [
            { name: 'T恤' },      // 移除了price字段
            { name: '衬衫' },
            { name: '羊毛衫' },
            // ...
        ]
    },
    // ...
];
```

#### 价格获取函数修改
```javascript
/**
 * 根据衣物名称获取价格（已废弃基础价格概念）
 * @param {string} name 衣物名称
 * @returns {number} 价格（始终返回0，因为已移除基础价格）
 * @deprecated 基础价格概念已移除，请使用服务类型价格
 */
function getClothingPrice(name) {
    // 基础价格概念已移除，始终返回0
    // 应该使用 getServicePrice() 获取具体服务类型的价格
    console.warn('getClothingPrice() 已废弃，基础价格概念已移除，请使用 getServicePrice() 获取服务类型价格');
    return 0;
}
```

### 5. 前端JavaScript修改

#### 首页衣物选择逻辑
**文件**: `templates/index.html`

```javascript
// 基础价格概念已移除，选择商品时使用默认服务价格
if (this.value) {
    console.log(`选择商品: ${this.value}，使用默认服务价格`);

    // 使用数据库中的默认服务价格
    const washPriceInput = this.closest('.clothing-item').querySelector('.wash-price');
    if (washPriceInput && typeof getServicePrice === 'function') {
        washPriceInput.value = getServicePrice('洗衣').toFixed(2);
        if (clothingItemsData[itemIndex]) {
            clothingItemsData[itemIndex].specialRequirements.washPrice = getServicePrice('洗衣');
        }
    }
}
```

#### 移动端衣物选择逻辑
**文件**: `static/js/mobile-functions.js`

```javascript
// 基础价格概念已移除，选择商品时使用默认服务价格
if (selectedOption.value) {
    console.log(`移动端选择商品: ${selectedOption.value}，使用默认服务价格`);

    // 使用数据库中的默认服务价格
    const washPriceInput = newItem.querySelector('.wash-price');
    if (washPriceInput && typeof getServicePrice === 'function') {
        washPriceInput.value = getServicePrice('洗衣').toFixed(2);
    }
}
```

## 🔄 向后兼容性

### 数据库兼容性
- **保留price字段**：虽然不再使用，但保留在数据库中，确保现有数据不受影响
- **默认值设置**：新创建的商品price字段自动设为0.0
- **查询兼容**：现有的数据库查询不会因为字段存在而报错

### API兼容性
- **响应数据**：API响应中不再包含price字段，但不会影响只使用服务价格的客户端
- **请求数据**：API接口忽略传入的price字段，不会报错

### 前端兼容性
- **废弃函数**：getClothingPrice()函数标记为废弃但仍然存在，返回0并输出警告
- **价格计算**：所有价格计算逻辑改为使用服务类型价格

## 🧪 测试建议

### 功能测试
1. **商品管理**：
   - 创建新商品，验证只需要填写服务类型价格
   - 编辑现有商品，确认基础价格字段不显示
   - 删除商品功能正常

2. **订单创建**：
   - 选择衣物时使用默认服务价格
   - 价格计算基于选中的服务类型
   - 总价格 = 选中服务类型价格之和

3. **历史数据**：
   - 现有订单数据正常显示
   - 历史订单编辑功能正常
   - 打印功能不受影响

### 数据验证
1. **数据库检查**：
   - 新创建商品的price字段为0.0
   - 现有商品数据保持不变
   - 服务价格字段正常更新

2. **API响应检查**：
   - 商品列表不包含price字段
   - 商品详情不包含price字段
   - 创建/更新商品成功

## 📝 注意事项

1. **废弃警告**：使用getClothingPrice()函数会在控制台输出废弃警告
2. **数据迁移**：如需完全移除price字段，需要单独的数据库迁移脚本
3. **文档更新**：相关API文档需要更新，移除基础价格相关说明
4. **培训需要**：操作人员需要了解新的价格管理方式

## ✅ 修改完成状态

- [x] 数据库模型修改
- [x] API接口调整
- [x] 商品管理页面更新
- [x] 静态配置文件修改
- [x] 前端JavaScript更新
- [x] 向后兼容性保证
- [x] 文档编写

系统已成功移除基础价格概念，现在完全基于服务类型价格进行计算，价格逻辑更加清晰和一致。
