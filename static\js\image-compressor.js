/**
 * 高级图片压缩模块
 * 提供图片压缩、格式转换和尺寸调整功能
 */

// 默认配置
const DEFAULT_CONFIG = {
    maxWidth: 1280,           // 最大宽度
    maxHeight: 720,           // 最大高度
    quality: 0.7,             // JPEG/WebP质量 (0-1)
    useWebP: true,            // 是否使用WebP格式(如果浏览器支持)
    progressive: true,        // 是否使用渐进式JPEG
    autoDetectNetworkQuality: true, // 是否根据网络质量自动调整压缩参数
    minQuality: 0.5,          // 最低质量(慢网络)
    maxQuality: 0.85,         // 最高质量(快网络)
    networkTestUrl: 'https://www.baidu.com/favicon.ico', // 网络测试URL
    networkTestTimeout: 2000  // 网络测试超时时间(ms)
};

/**
 * 检测浏览器是否支持WebP格式
 * @returns {Promise<boolean>} 是否支持WebP
 */
function checkWebPSupport() {
    return new Promise(resolve => {
        const webP = new Image();
        webP.onload = () => resolve(true);
        webP.onerror = () => resolve(false);
        webP.src = 'data:image/webp;base64,UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==';
    });
}

/**
 * 检测当前网络质量
 * @param {Object} config 配置对象
 * @returns {Promise<number>} 网络质量系数 (0-1)
 */
async function detectNetworkQuality(config) {
    const startTime = Date.now();
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.networkTestTimeout);
        
        await fetch(config.networkTestUrl + '?_=' + Date.now(), {
            method: 'HEAD',
            mode: 'no-cors',
            cache: 'no-store',
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // 计算网络质量系数 (0-1)
        // 小于500ms为最佳，大于1500ms为最差
        const qualityFactor = Math.max(0, Math.min(1, 1 - (duration - 500) / 1000));
        console.log(`网络质量检测: ${duration}ms, 质量系数: ${qualityFactor.toFixed(2)}`);
        return qualityFactor;
    } catch (error) {
        console.warn('网络质量检测失败:', error);
        return 0.5; // 默认中等质量
    }
}

/**
 * 根据网络质量调整压缩参数
 * @param {Object} config 配置对象
 * @returns {Promise<Object>} 调整后的配置
 */
async function adjustConfigByNetwork(config) {
    if (!config.autoDetectNetworkQuality) {
        return config;
    }
    
    const networkQuality = await detectNetworkQuality(config);
    const adjustedConfig = {...config};
    
    // 根据网络质量调整压缩质量
    adjustedConfig.quality = config.minQuality + networkQuality * (config.maxQuality - config.minQuality);
    
    // 根据网络质量调整最大尺寸
    if (networkQuality < 0.3) {
        // 慢网络，降低尺寸
        adjustedConfig.maxWidth = Math.min(config.maxWidth, 800);
        adjustedConfig.maxHeight = Math.min(config.maxHeight, 600);
    }
    
    console.log('根据网络质量调整配置:', adjustedConfig);
    return adjustedConfig;
}

/**
 * 压缩图片
 * @param {string} dataUrl 原始图片的dataURL
 * @param {Object} userConfig 用户配置
 * @returns {Promise<string>} 压缩后的dataURL
 */
async function compressImage(dataUrl, userConfig = {}) {
    // 合并配置
    let config = {...DEFAULT_CONFIG, ...userConfig};
    
    // 根据网络质量调整配置
    config = await adjustConfigByNetwork(config);
    
    // 检测WebP支持
    const supportsWebP = config.useWebP && await checkWebPSupport();
    
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = function() {
            try {
                // 计算新尺寸，保持宽高比
                let {width, height} = calculateDimensions(img.width, img.height, config);
                
                // 创建Canvas
                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                
                // 绘制图像
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);
                
                // 确定输出格式和选项
                let outputFormat = 'image/jpeg';
                let outputOptions = {};
                
                if (supportsWebP) {
                    outputFormat = 'image/webp';
                }
                
                // 使用toBlob进行高级压缩
                canvas.toBlob(blob => {
                    if (!blob) {
                        reject(new Error('图片压缩失败'));
                        return;
                    }
                    
                    // 转换为dataURL
                    const reader = new FileReader();
                    reader.onload = function() {
                        const compressedDataUrl = reader.result;
                        console.log(`压缩前大小: ${Math.round(dataUrl.length / 1024)}KB, 压缩后: ${Math.round(compressedDataUrl.length / 1024)}KB`);
                        resolve(compressedDataUrl);
                    };
                    reader.onerror = function() {
                        reject(new Error('图片数据读取失败'));
                    };
                    reader.readAsDataURL(blob);
                }, outputFormat, config.quality);
            } catch (error) {
                reject(error);
            }
        };
        
        img.onerror = function() {
            reject(new Error('图片加载失败'));
        };
        
        img.src = dataUrl;
    });
}

/**
 * 计算新的图片尺寸，保持宽高比
 * @param {number} originalWidth 原始宽度
 * @param {number} originalHeight 原始高度
 * @param {Object} config 配置
 * @returns {Object} 新的尺寸 {width, height}
 */
function calculateDimensions(originalWidth, originalHeight, config) {
    let width = originalWidth;
    let height = originalHeight;
    
    // 检查是否需要调整尺寸
    if (width > config.maxWidth) {
        height = Math.round(height * config.maxWidth / width);
        width = config.maxWidth;
    }
    
    if (height > config.maxHeight) {
        width = Math.round(width * config.maxHeight / height);
        height = config.maxHeight;
    }
    
    // 确保尺寸为偶数，某些编码器需要
    width = Math.floor(width / 2) * 2;
    height = Math.floor(height / 2) * 2;
    
    return {width, height};
}

/**
 * 获取设备屏幕信息
 * @returns {Object} 设备屏幕信息
 */
function getDeviceInfo() {
    const devicePixelRatio = window.devicePixelRatio || 1;
    const screenWidth = window.screen.width * devicePixelRatio;
    const screenHeight = window.screen.height * devicePixelRatio;
    
    return {
        pixelRatio: devicePixelRatio,
        screenWidth,
        screenHeight,
        isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    };
}

/**
 * 根据设备自动配置压缩参数
 * @returns {Object} 设备优化的配置
 */
function getDeviceOptimizedConfig() {
    const deviceInfo = getDeviceInfo();
    const config = {...DEFAULT_CONFIG};
    
    // 根据设备调整最大尺寸
    if (deviceInfo.isMobile) {
        // 移动设备使用较小的尺寸
        config.maxWidth = Math.min(1280, deviceInfo.screenWidth);
        config.maxHeight = Math.min(720, deviceInfo.screenHeight);
        
        // 移动设备可能网络较慢，降低默认质量
        config.quality = 0.65;
    } else {
        // 桌面设备使用较大的尺寸
        config.maxWidth = Math.min(1920, deviceInfo.screenWidth);
        config.maxHeight = Math.min(1080, deviceInfo.screenHeight);
    }
    
    return config;
}

// 导出模块
window.ImageCompressor = {
    compress: compressImage,
    getDeviceOptimizedConfig,
    checkWebPSupport,
    detectNetworkQuality
};
