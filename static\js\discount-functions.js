/**
 * 商场客户折扣计算功能模块
 * 提供自动折扣计算、有效期验证、冲突处理等功能
 */

// 全局变量存储当前商场客户的折扣信息
window.mallCustomerDiscounts = [];
window.currentMallCustomer = null;

/**
 * 检查折扣是否在有效期内
 * @param {Object} discount 折扣对象
 * @returns {boolean} 是否有效
 */
function isDiscountValid(discount) {
    const today = new Date();
    const effectiveDate = new Date(discount.effective_date);

    // 检查生效日期
    if (today < effectiveDate) {
        console.log(`折扣未生效: ${discount.product_name} - 生效日期: ${discount.effective_date}`);
        return false;
    }

    // 检查到期日期（如果有）
    if (discount.expiry_date && discount.expiry_date !== '长期有效') {
        const expiryDate = new Date(discount.expiry_date);
        if (today > expiryDate) {
            console.log(`折扣已过期: ${discount.product_name} - 到期日期: ${discount.expiry_date}`);
            return false;
        }
    }

    return true;
}

/**
 * 查找匹配的折扣
 * @param {string} productName 产品名称
 * @param {string} productType 产品类型
 * @returns {Object|null} 最佳匹配的折扣对象
 */
function findBestDiscount(productName, productType) {
    console.log(`🔍 查找最佳折扣: 商品=${productName}, 类型=${productType}`);

    if (!window.mallCustomerDiscounts || window.mallCustomerDiscounts.length === 0) {
        console.log('  ❌ 无折扣数据');
        return null;
    }

    console.log(`  📋 总折扣数量: ${window.mallCustomerDiscounts.length}`);

    // 过滤有效的折扣
    const validDiscounts = window.mallCustomerDiscounts.filter(discount => isDiscountValid(discount));

    if (validDiscounts.length === 0) {
        console.log(`  ❌ 没有找到有效的折扣: ${productName} (${productType})`);
        return null;
    }

    console.log(`  ✓ 有效折扣数量: ${validDiscounts.length}`);

    // 查找匹配的折扣
    const matchingDiscounts = [];

    // 1. 产品名称完全匹配
    const exactNameMatches = validDiscounts.filter(discount =>
        discount.product_name === productName
    );
    console.log(`  🎯 产品名称匹配数量: ${exactNameMatches.length}`);

    // 2. 产品类型匹配
    const typeMatches = validDiscounts.filter(discount =>
        discount.product_type === productType && discount.product_name !== productName
    );
    console.log(`  🏷️ 产品类型匹配数量: ${typeMatches.length}`);

    // 按优先级添加匹配的折扣
    matchingDiscounts.push(...exactNameMatches);
    matchingDiscounts.push(...typeMatches);

    if (matchingDiscounts.length === 0) {
        console.log(`  ❌ 没有找到匹配的折扣: ${productName} (${productType})`);
        return null;
    }

    console.log(`  ✓ 总匹配折扣数量: ${matchingDiscounts.length}`);

    // 折扣冲突处理：按优先级排序
    matchingDiscounts.sort((a, b) => {
        // 1. 产品名称完全匹配优先
        const aExactMatch = a.product_name === productName;
        const bExactMatch = b.product_name === productName;
        if (aExactMatch && !bExactMatch) return -1;
        if (!aExactMatch && bExactMatch) return 1;

        // 2. 折扣率最低的优先（对客户最优惠）
        if (a.discount_rate !== b.discount_rate) {
            return a.discount_rate - b.discount_rate;
        }

        // 3. 最新创建的优先
        return new Date(b.created_at) - new Date(a.created_at);
    });

    const bestDiscount = matchingDiscounts[0];

    console.log(`  🏆 最佳折扣: ${bestDiscount.product_name} - ${bestDiscount.product_type} - ${(bestDiscount.discount_rate * 10).toFixed(1)}折`);
    if (matchingDiscounts.length > 1) {
        console.log(`  ⚖️ 折扣冲突处理: 从${matchingDiscounts.length}个匹配折扣中选择最优惠的`);
        matchingDiscounts.forEach((discount, index) => {
            console.log(`    ${index + 1}. ${discount.product_name} - ${discount.product_type} - ${(discount.discount_rate * 10).toFixed(1)}折`);
        });
    }

    return bestDiscount;
}

/**
 * 应用商场客户折扣到衣物价格
 * @param {string} productName 产品名称
 * @param {string} productType 产品类型
 * @param {number} originalPrice 原价
 * @returns {Object} 包含折扣信息的对象
 */
function applyMallCustomerDiscount(productName, productType, originalPrice) {
    // 如果不是商场客户，返回原价
    if (!window.currentMallCustomer) {
        console.log('折扣计算: 非商场客户，使用原价');
        return {
            originalPrice: originalPrice,
            discountRate: 1.0,
            discountedPrice: originalPrice,
            discountAmount: 0,
            discountInfo: null
        };
    }

    console.log('=== 开始折扣计算 ===');
    console.log(`商场客户: ${window.currentMallCustomer.mall_name} (ID: ${window.currentMallCustomer.id})`);
    console.log(`商品: ${productName}, 类型: ${productType}, 原价: ¥${originalPrice}`);
    console.log(`客户整体折扣率: ${window.currentMallCustomer.overall_discount_rate}`);

    // 查找最佳折扣
    const bestDiscount = findBestDiscount(productName, productType);

    // 获取整体折扣率
    const overallDiscountRate = window.currentMallCustomer.overall_discount_rate || 1.0;
    console.log(`获取到的整体折扣率: ${overallDiscountRate}`);

    // 折扣逻辑：单独折扣和整体折扣互斥
    let discountedPrice = originalPrice;
    let appliedDiscountRate = 1.0;
    let discountInfo = null;

    if (bestDiscount) {
        // 如果有单独的产品折扣，使用单独折扣，不叠加整体折扣
        discountedPrice = originalPrice * bestDiscount.discount_rate;
        appliedDiscountRate = bestDiscount.discount_rate;
        discountInfo = {
            productDiscount: bestDiscount,
            overallDiscount: null
        };
        console.log(`✓ 应用单品折扣: ${(bestDiscount.discount_rate * 10).toFixed(1)}折 (不叠加整体折扣)`);
        console.log(`  单品折扣详情: ${bestDiscount.product_name} - ${bestDiscount.product_type} - ${bestDiscount.discount_rate}`);
    } else if (overallDiscountRate < 1.0) {
        // 如果没有单独折扣但有整体折扣，使用整体折扣
        discountedPrice = originalPrice * overallDiscountRate;
        appliedDiscountRate = overallDiscountRate;
        discountInfo = {
            productDiscount: null,
            overallDiscount: overallDiscountRate
        };
        console.log(`✓ 应用整体折扣: ${(overallDiscountRate * 10).toFixed(1)}折 (无单品折扣)`);
        console.log(`  整体折扣计算: ¥${originalPrice} × ${overallDiscountRate} = ¥${discountedPrice.toFixed(2)}`);
    } else {
        // 没有任何折扣
        console.log(`❌ 无折扣应用: ${productName} (无单品折扣且整体折扣率为${overallDiscountRate})`);
        console.log('=== 折扣计算结束 ===');
        return {
            originalPrice: originalPrice,
            discountRate: 1.0,
            discountedPrice: originalPrice,
            discountAmount: 0,
            discountInfo: null
        };
    }

    const totalDiscountAmount = originalPrice - discountedPrice;

    console.log(`✅ 折扣计算完成: ${productName}`);
    console.log(`   原价: ¥${originalPrice}`);
    console.log(`   折后价: ¥${discountedPrice.toFixed(2)}`);
    console.log(`   折扣金额: ¥${totalDiscountAmount.toFixed(2)}`);
    console.log(`   折扣率: ${(appliedDiscountRate * 10).toFixed(1)}折`);
    console.log('=== 折扣计算结束 ===');

    return {
        originalPrice: originalPrice,
        discountRate: appliedDiscountRate,
        discountedPrice: discountedPrice,
        discountAmount: totalDiscountAmount,
        discountInfo: discountInfo
    };
}

/**
 * 格式化折扣显示信息
 * @param {Object} discountResult 折扣计算结果
 * @returns {string} 格式化的折扣信息
 */
function formatDiscountInfo(discountResult) {
    if (!discountResult.discountInfo) {
        return '';
    }

    const { originalPrice, discountedPrice, discountInfo } = discountResult;

    let info = `原价 ¥${originalPrice.toFixed(2)} → 折后 ¥${discountedPrice.toFixed(2)}`;

    if (discountInfo.productDiscount) {
        info += ` (单品${(discountInfo.productDiscount.discount_rate * 10).toFixed(1)}折)`;
    } else if (discountInfo.overallDiscount) {
        info += ` (整体${(discountInfo.overallDiscount * 10).toFixed(1)}折)`;
    }

    return info;
}

/**
 * 更新衣物价格输入框的折扣显示
 * @param {Element} priceInput 价格输入框元素
 * @param {Object} discountResult 折扣计算结果
 */
function updatePriceInputWithDiscount(priceInput, discountResult) {
    if (!priceInput) return;

    // 更新价格输入框的值
    priceInput.value = discountResult.discountedPrice.toFixed(2);

    // 添加或更新折扣信息显示
    let discountDisplay = priceInput.parentElement.querySelector('.discount-info');

    if (discountResult.discountAmount > 0) {
        if (!discountDisplay) {
            discountDisplay = document.createElement('div');
            discountDisplay.className = 'discount-info';
            discountDisplay.style.cssText = 'font-size: 12px; color: #e91e63; margin-top: 2px;';
            priceInput.parentElement.appendChild(discountDisplay);
        }

        discountDisplay.textContent = formatDiscountInfo(discountResult);
        discountDisplay.title = `节省 ¥${discountResult.discountAmount.toFixed(2)}`;
    } else if (discountDisplay) {
        discountDisplay.remove();
    }

    // 存储折扣信息到元素的数据属性中
    priceInput.dataset.originalPrice = discountResult.originalPrice;
    priceInput.dataset.discountRate = discountResult.discountRate;
    priceInput.dataset.discountAmount = discountResult.discountAmount;
}

/**
 * 计算订单总折扣金额
 * @returns {number} 总折扣金额
 */
function calculateTotalDiscountAmount() {
    let totalDiscount = 0;

    // 获取所有价格输入框（避免重复计算）
    const priceInputs = document.querySelectorAll('.clothing-price');
    priceInputs.forEach(input => {
        const discountAmount = parseFloat(input.dataset.discountAmount || 0);
        totalDiscount += discountAmount;
    });

    return totalDiscount;
}

/**
 * 更新订单总计区域的折扣显示
 */
function updateOrderTotalDiscountDisplay() {
    const totalDiscount = calculateTotalDiscountAmount();

    // 查找现有的折扣显示元素
    let discountDisplay = document.querySelector('.order-total-discount');

    if (totalDiscount <= 0) {
        // 没有折扣时，移除显示元素
        if (discountDisplay) {
            discountDisplay.remove();
        }
        return;
    }

    if (!discountDisplay) {
        // 查找订单总计区域
        const totalAmountElement = document.querySelector('#totalAmount') ||
                                 document.querySelector('.total-amount') ||
                                 document.querySelector('[data-total-amount]');

        if (totalAmountElement) {
            discountDisplay = document.createElement('div');
            discountDisplay.className = 'order-total-discount';
            discountDisplay.style.cssText = 'color: #e91e63; font-weight: bold; margin-top: 5px;';
            totalAmountElement.parentElement.appendChild(discountDisplay);
        }
    }

    if (discountDisplay) {
        discountDisplay.textContent = `总折扣: ¥${totalDiscount.toFixed(2)}`;
    }
}

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        isDiscountValid,
        findBestDiscount,
        applyMallCustomerDiscount,
        formatDiscountInfo,
        updatePriceInputWithDiscount,
        calculateTotalDiscountAmount,
        updateOrderTotalDiscountDisplay
    };
}
