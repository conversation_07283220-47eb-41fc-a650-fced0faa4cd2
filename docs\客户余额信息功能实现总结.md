# 客户余额信息功能实现总结

## 功能概述

为收据打印功能添加了客户余额信息显示，包括订单前余额、本次消费金额（仅余额支付时）和订单后余额。

## 修改的文件

### 1. 后端修改

#### `app.py`
- **修改位置**: `/order_details` 路由的响应数据构建部分
- **修改内容**: 添加了客户余额信息计算逻辑
- **功能**: 
  - 计算订单前后的客户余额
  - 判断是否为余额支付
  - 计算本次消费的余额金额
  - 将余额信息添加到API响应中

### 2. 前端JavaScript修改

#### `static/js/print-functions.js`
- **新增函数**: `generateBalanceInfoHTML(orderData)`
- **修改函数**: `generateReceiptHTML(orderData)`
- **功能**: 
  - 生成客户余额信息的HTML内容
  - 在小票模板中集成余额信息显示
  - 添加支付状态和支付时间显示

#### `static/js/receipt-print.js`
- **新增函数**: `generateBalanceInfoHTML(orderData)`
- **修改函数**: `generateReceiptHTML(orderData)`
- **功能**: 
  - 与print-functions.js保持一致的余额信息显示
  - 修复数量计算问题
  - 添加支付状态和支付时间显示

### 3. 模板修改

#### `templates/receipt.html`
- **修改位置**: 支付信息部分
- **修改内容**: 添加客户余额信息显示逻辑
- **功能**: 
  - 检查客户是否有余额账户
  - 根据支付方式显示不同的余额信息
  - 支持移动端打印功能

### 4. 样式修改

#### `static/css/print-styles.css`
- **新增样式**: `.receipt-balance-info` 相关样式
- **功能**: 
  - 确保余额信息在打印时格式正确
  - 添加分隔线和适当的间距
  - 设置合适的字体大小和行高

### 5. 文档更新

#### `docs/打印功能维护指南.md`
- **新增内容**: 客户余额信息显示说明
- **功能**: 
  - 说明余额信息的显示逻辑
  - 提供修改余额信息格式的指导
  - 添加相关CSS样式说明

## 功能特性

### 1. 显示条件
- 只有当客户有余额账户时才显示余额信息
- 对于商场客户或没有余额记录的客户，不显示此部分

### 2. 显示内容
- **订单前余额**: 显示本次订单前的客户余额
- **本次消费**: 仅在余额支付时显示消费金额
- **订单后余额**: 显示本次订单后的客户余额

### 3. 支付方式适配
- **余额支付**: 显示完整的余额变化信息
- **其他支付方式**: 只显示当前余额

### 4. 多平台支持
- **桌面端**: 通过JavaScript生成的HTML模板
- **移动端**: 通过服务器渲染的HTML模板
- **历史订单**: 支持历史订单的打印功能

## 数据流程

1. **API层**: `/order_details` 接口计算并返回客户余额信息
2. **JavaScript层**: `generateBalanceInfoHTML` 函数生成余额信息HTML
3. **模板层**: `receipt.html` 模板渲染余额信息
4. **样式层**: `print-styles.css` 确保打印格式正确

## 测试

创建了 `test_balance_info.html` 测试页面，包含以下测试用例：
- 余额支付订单
- 非余额支付订单
- 无余额账户订单

## 兼容性

- 向后兼容：对于没有余额信息的订单，不会显示余额部分
- 跨平台：支持桌面端和移动端的打印功能
- 多浏览器：使用标准CSS和JavaScript，确保兼容性

## 维护说明

1. **修改余额信息格式**: 编辑 `generateBalanceInfoHTML` 函数
2. **调整样式**: 修改 `print-styles.css` 中的 `.receipt-balance-info` 样式
3. **添加新字段**: 在 `app.py` 的余额信息计算逻辑中添加
4. **移动端适配**: 同时修改 `receipt.html` 模板

## 注意事项

1. 确保所有打印功能文件中的 `generateReceiptHTML` 函数保持一致
2. 余额计算逻辑需要与订单处理逻辑保持同步
3. 打印样式需要在不同设备上进行测试
4. 移动端和桌面端的实现方式不同，需要分别维护
