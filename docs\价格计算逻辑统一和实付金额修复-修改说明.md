# 价格计算逻辑统一和实付金额修复 - 修改说明

## 修改概述

本次修改完成了第二步任务：统一价格计算逻辑并修复实付价格计算错误。主要解决了实付金额计算错误的问题，并确保新订单创建和历史订单编辑使用完全相同的价格计算方式。

## 🔍 问题分析

### 1. **实付金额计算错误**

**错误场景**：
- 商品原价20元，享受五折优惠后应为10元
- **错误结果**：实付金额显示为0元
- **正确结果**：实付金额应该是10元

**错误原因**：
- 在多个地方使用了错误的计算公式：`实付金额 = 订单总金额 - 折扣金额`
- 但 `order.total_amount` 已经是折扣后的实际金额，再减去 `discount_amount` 就变成了负数或0

### 2. **数据结构不一致**

**问题**：
- 在不同的地方，价格字段的含义不一致
- 前端JavaScript中使用了不存在的 `actual_amount` 字段

## 🔧 修复内容

### 修复1: 小票模板中的实付金额计算

**文件**: `templates/receipt1.html` (第244-253行)

**修复前**：
```html
<div class="receipt-total">
    <p>总计: ¥{{ "%.2f"|format(order.total_amount) }}</p>
    {% if order.discount_amount > 0 %}
    <p>折扣: ¥{{ "%.2f"|format(order.discount_amount) }}</p>
    <p>实付: ¥{{ "%.2f"|format(order.total_amount - order.discount_amount) }}</p>
    {% endif %}
</div>
```

**修复后**：
```html
<div class="receipt-total">
    <p>数量: 共{{ clothes | sum(attribute='quantity') or clothes | length }}件</p>
    {% if order.discount_amount > 0 %}
    <p>原价: ¥{{ "%.2f"|format(order.total_amount + order.discount_amount) }}</p>
    <p>折扣: ¥{{ "%.2f"|format(order.discount_amount) }}</p>
    <p>实付: ¥{{ "%.2f"|format(order.total_amount) }}</p>
    {% else %}
    <p>总计: ¥{{ "%.2f"|format(order.total_amount) }}</p>
    {% endif %}
</div>
```

**改进**：
- 实付金额直接使用 `order.total_amount`（折扣后的金额）
- 原价计算为 `order.total_amount + order.discount_amount`
- 根据是否有折扣显示不同的信息

### 修复2: JavaScript打印函数中的实付金额计算

**文件**: `static/js/print-functions.js` (第79-89行)

**修复前**：
```javascript
<div class="receipt-total">
    <p><strong>总金额: ¥${orderData.total_amount.toFixed(2)}</strong></p>
    ${orderData.discount_amount > 0 ? `<p>折扣: -¥${orderData.discount_amount.toFixed(2)}</p>` : ''}
    <p><strong>实付金额: ¥${orderData.actual_amount.toFixed(2)}</strong></p>
    <p>支付方式: ${orderData.payment_method}</p>
    ${balanceInfo}
</div>
```

**修复后**：
```javascript
<div class="receipt-total">
    ${orderData.discount_amount > 0 ? `
        <p>原价: ¥${(orderData.total_amount + orderData.discount_amount).toFixed(2)}</p>
        <p>折扣: -¥${orderData.discount_amount.toFixed(2)}</p>
        <p><strong>实付金额: ¥${orderData.total_amount.toFixed(2)}</strong></p>
    ` : `
        <p><strong>总金额: ¥${orderData.total_amount.toFixed(2)}</strong></p>
    `}
    <p>支付方式: ${orderData.payment_method}</p>
    ${balanceInfo}
</div>
```

**改进**：
- 移除了对不存在的 `orderData.actual_amount` 字段的引用
- 实付金额直接使用 `orderData.total_amount`
- 统一了有折扣和无折扣情况下的显示逻辑

### 修复3: 余额信息计算中的实付金额引用

**文件**: `static/js/print-functions.js` (第240行和第260行)

**修复前**：
```javascript
const actualAmount = parseFloat(orderData.actual_amount) || 0;
```

**修复后**：
```javascript
const actualAmount = parseFloat(orderData.total_amount) || 0;  // 使用total_amount作为实付金额
```

**改进**：
- 统一使用 `orderData.total_amount` 作为实付金额
- 确保余额计算的准确性

### 修复4: 后端API响应数据结构

**文件**: `app.py` (第1454-1475行)

**修复前**：
```python
order_data = {
    'total_amount': order.total_amount,
    'discount_amount': order.discount_amount,
    'actual_amount': order.total_amount - order.discount_amount,  # 错误计算
    # ...
}
```

**修复后**：
```python
order_data = {
    'total_amount': order.total_amount,  # 实际应付金额（折扣后）
    'discount_amount': order.discount_amount or 0,  # 折扣金额
    'original_amount': (order.total_amount or 0) + (order.discount_amount or 0),  # 原始金额（折扣前）
    'actual_amount': order.total_amount,  # 实付金额等于total_amount（折扣后的金额）
    # ...
}
```

**改进**：
- 修正了 `actual_amount` 的计算逻辑
- 添加了 `original_amount` 字段用于显示原价
- 添加了空值保护，避免计算错误

### 修复5: 历史订单详情页面的实付金额显示

**文件**: `templates/history.html` (第1754-1756行)

**修复前**：
```javascript
<div class="modal-info-item">
    <div class="modal-info-label">实际金额:</div>
    <div>¥${order.actual_amount.toFixed(2)}</div>
</div>
```

**修复后**：
```javascript
<div class="modal-info-item">
    <div class="modal-info-label">实付金额:</div>
    <div>¥${order.total_amount.toFixed(2)}</div>
</div>
```

**改进**：
- 使用 `order.total_amount` 替代不存在的 `order.actual_amount`
- 统一了术语，使用"实付金额"而不是"实际金额"

## ✅ 价格计算逻辑统一验证

### 新订单创建过程
- 使用累加模式：总价 = 洗衣价格 + 织补价格 + 改衣价格 + 其他价格
- 支持所有四种服务类型：洗衣、织补、改衣、其他

### 历史订单编辑过程
- 使用相同的累加模式：`totalPrice = basePrice + darningPrice + alteringPrice + otherPrice`
- 支持所有四种服务类型：洗衣、织补、改衣、其他
- 与新订单创建逻辑完全一致

## 🎯 修复效果

### 修复前的错误场景
- **商品原价**：20元
- **折扣率**：50%
- **订单总金额**：10元 ✓（正确）
- **折让金额**：10元 ✓（正确）
- **实付金额**：0元 ❌（错误）

### 修复后的正确结果
- **商品原价**：20元
- **折扣率**：50%
- **订单总金额**：10元 ✓（正确）
- **折让金额**：10元 ✓（正确）
- **实付金额**：10元 ✅（正确）

## 📋 数据字段含义统一

### 统一后的字段定义
- `order.total_amount`：实际应付金额（折扣后的最终金额）
- `order.discount_amount`：折扣金额（原价与折后价的差额）
- `order.original_amount`：原始金额（折扣前的金额）
- **实付金额** = `order.total_amount`
- **原价** = `order.total_amount + order.discount_amount`

### 计算公式
```
原价 = 实付金额 + 折扣金额
实付金额 = 原价 - 折扣金额 = order.total_amount
折扣金额 = 原价 - 实付金额 = order.discount_amount
```

## 🧪 测试建议

### 1. 基本价格计算测试
- 创建包含折扣的订单，验证实付金额显示正确
- 测试无折扣订单的价格显示
- 验证小票打印中的价格信息

### 2. 历史订单编辑测试
- 编辑包含多种服务类型的订单
- 验证价格计算与新订单创建一致
- 测试"其他"服务的价格处理

### 3. 打印功能测试
- 测试小票打印中的实付金额显示
- 验证余额信息计算的准确性
- 测试不同支付方式的显示

### 4. 边界情况测试
- 测试折扣金额为0的情况
- 测试价格为0的服务项目
- 测试数据库中旧格式数据的兼容性

## 🎉 修复完成

此次修复彻底解决了实付金额计算错误的问题，并确保了新订单创建和历史订单编辑功能使用完全统一的价格计算逻辑。所有价格相关的显示和计算都基于正确的业务逻辑，为用户提供准确、一致的价格信息。
