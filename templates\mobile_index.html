<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Soulweave改衣坊</title>
    <link rel="stylesheet" href="/static/css/print-styles.css">
    <link rel="stylesheet" href="/static/css/mobile-styles.css">
    <script src="/static/js/image-compressor.js"></script>
    <script src="/static/js/discount-functions.js"></script>

    <!-- 页面解析时立即执行的脚本，确保加载指示器在页面解析时就被隐藏 -->
    <script>
        // 设置一个全局变量，表示页面正在加载
        window.pageIsLoading = true;

        // 在页面解析时就尝试隐藏加载指示器
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded 事件触发');
            hideLoadingIndicator();
        });

        // 在页面完全加载后再次尝试隐藏加载指示器
        window.onload = function() {
            console.log('window.onload 事件触发');
            hideLoadingIndicator();
            window.pageIsLoading = false;
        };

        // 隐藏加载指示器的函数
        function hideLoadingIndicator() {
            console.log('尝试隐藏加载指示器');
            var processingIndicator = document.getElementById('processingIndicator');
            if (processingIndicator) {
                processingIndicator.style.display = 'none';
                console.log('成功隐藏加载指示器');
            } else {
                console.log('未找到加载指示器元素');
                // 如果元素还不存在，等待一段时间后再次尝试
                setTimeout(function() {
                    var indicator = document.getElementById('processingIndicator');
                    if (indicator) {
                        indicator.style.display = 'none';
                        console.log('延迟后成功隐藏加载指示器');
                    }
                }, 100);
            }
        }

        // 立即尝试隐藏加载指示器
        hideLoadingIndicator();
    </script>
    <style>
        /* 移动端基础样式 */
        * {
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            padding-bottom: 72px; /* 为底部菜单留出空间 */
            background-color: #f9fafb;
            color: #1f2937;
            font-size: 14px; /* 紧凑化字体 */
            line-height: 1.4; /* 紧凑化行高 */
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 头部样式 - 现代化设计 */
        .header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: var(--space-lg, 24px) var(--space-md, 16px);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            margin: 0;
            font-size: var(--font-xl, 20px);
            font-weight: 700;
            text-align: center;
            letter-spacing: -0.025em;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* 导航菜单 - 现代化设计 */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary, white);
            backdrop-filter: blur(20px);
            border-top: 1px solid var(--border-light, #e5e7eb);
            padding: var(--space-sm, 8px) var(--space-md, 16px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 1000;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            height: 72px;
        }

        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--text-tertiary, #9ca3af);
            transition: all 0.2s ease;
            padding: var(--space-sm, 8px);
            border-radius: var(--radius-lg, 12px);
            min-width: 60px;
            position: relative;
        }

        .menu-item:hover {
            color: var(--primary-color, #6366f1);
            background: var(--bg-tertiary, #f3f4f6);
            transform: translateY(-2px);
        }

        .menu-item.active {
            color: var(--primary-color, #6366f1);
            background: rgba(99, 102, 241, 0.1);
        }

        .menu-icon {
            font-size: var(--font-lg, 18px);
            margin-bottom: var(--space-xs, 4px);
            transition: transform 0.2s ease;
        }

        .menu-item:hover .menu-icon {
            transform: scale(1.1);
        }

        .menu-item span {
            font-size: var(--font-xs, 12px);
            font-weight: 500;
            line-height: 1;
            text-align: center;
        }

        /* 标签页导航 - 紧凑化且高对比度 */
        .tab-navigation {
            display: flex;
            background: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 90;
            height: 48px;
            padding: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .tab-button {
            flex: 1;
            padding: 8px 4px;
            text-align: center;
            background: transparent;
            border: none;
            font-size: 13px;
            font-weight: 500;
            color: #6b7280;
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            height: 40px;
            border-radius: 8px;
            margin: 0 2px;
            transition: all 0.2s ease;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .tab-button:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .tab-button.active {
            background: #6366f1;
            color: #ffffff;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
        }

        .tab-button span {
            line-height: 1.2;
            left: 25%;
            width: 50%;
            height: 3px;
            background: var(--primary-color, #8e44ad);
            border-radius: 3px 3px 0 0;
        }

        /* 内容区域 - 紧凑化设计 */
        .tab-content {
            display: none;
            padding: 12px;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        /* 表单元素 - 紧凑化设计 */
        .form-group {
            margin-bottom: 12px;
        }

        label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #374151;
            font-size: 13px;
        }

        input, select, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            background-color: white;
            height: 36px; /* 紧凑化高度 */
            margin-bottom: 0;
            box-sizing: border-box;
            transition: border-color 0.2s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
        }

        /* 将所有textarea改为单行输入框样式 */
        textarea {
            height: 36px;
            resize: none;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        button {
            background: #6366f1;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            width: 100%;
            cursor: pointer;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
            height: 36px; /* 紧凑化高度 */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        button:hover {
            background: #4f46e5;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.15);
        }

        button:active {
            background: #4338ca;
            transform: translateY(0);
            transform: translateY(1px);
        }

        /* 底部操作按钮 - 与mobile-styles.css保持一致 */
        .action-button {
            position: fixed;
            bottom: 45px;
            right: 12px;
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: var(--primary-color, #8e44ad);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 999;
            font-size: 1.2rem;
            font-weight: bold;
            border: none;
            cursor: pointer;
            transition: transform 0.2s, background-color 0.2s;
        }

        .action-button:active {
            transform: scale(0.95);
            background-color: var(--primary-dark, #6c3483);
        }

        /* 下一步按钮 */
        .next-step {
            background: var(--secondary-color, #27ae60);
            margin-top: 12px;
            padding: 8px 12px;
        }

        /* 卡片样式 - 紧凑化 */
        .card {
            background: white;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }

        .card-header {
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f3f4f6;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        /* 衣物项样式 - 紧凑化 */
        .clothing-item {
            background: white;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
            position: relative;
        }

        .clothing-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .clothing-item-title {
            font-weight: bold;
            margin: 0;
            font-size: 1.1rem;
        }

        .toggle-collapse {
            background: none;
            color: #007BFF;
            box-shadow: none;
            width: auto;
            padding: 5px 10px;
            font-size: 0.9rem;
        }

        .remove-item {
            position: absolute;
            right: 10px;
            top: 10px;
            background: #ff4d4d;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            padding: 0;
            box-shadow: 0 2px 5px rgba(255,77,77,0.3);
        }

        .clothing-content {
            margin-top: 15px;
        }

        .clothing-item.collapsed .clothing-content {
            display: none;
        }

        /* 服务类型 - 单行显示 */
        .service-types {
            display: flex;
            gap: 6px;
            margin-top: 8px;
            flex-wrap: nowrap; /* 强制单行显示 */
        }

        .service-types label {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            background: #f3f4f6;
            padding: 6px 8px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            flex: 1; /* 平均分配宽度 */
            min-width: 0; /* 允许收缩 */
            text-align: center;
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
            cursor: pointer;
            user-select: none;
        }

        .service-types label:hover {
            background: #e5e7eb;
            border-color: #d1d5db;
        }

        .service-types label.selected {
            background: #6366f1;
            color: white;
            border-color: #6366f1;
        }

        .service-types input[type="checkbox"] {
            display: none; /* 隐藏原生复选框 */
        }

        /* 照片区域 */
        .photo-gallery {
            margin: 15px 0;
        }

        .photo-thumbnails {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
        }

        .photo-thumbnail {
            width: 70px;
            height: 70px;
            border-radius: 8px;
            object-fit: cover;
            border: 1px solid #ddd;
        }

        .photo-thumbnail-container {
            position: relative;
        }

        .remove-photo {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background-color: #ff4d4d;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 10;
        }

        .add-photo-btn {
            background: #f5f5f5;
            color: #666;
            border: 1px dashed #ccc;
            padding: 10px;
        }
    </style>
</head>
<body>
    <!-- 加载指示器 - 初始状态为显示 -->
    <div id="processingIndicator" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255,255,255,0.9); display: flex; flex-direction: column; justify-content: center; align-items: center; z-index: 9999;">
        <div style="width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #007BFF; border-radius: 50%; animation: spin 1s linear infinite;"></div>
        <p id="processingText" style="margin-top: 20px; font-size: 16px; color: #333;">加载中...</p>
    </div>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <!-- 移除头部标题栏以释放更多屏幕空间 -->

    <!-- 标签页导航 -->
    <div class="tab-navigation">
        <button class="tab-button active" data-tab="customer-tab">
            <div style="display: flex; flex-direction: column; align-items: center;">
                <span style="font-size: 1.1rem; margin-bottom: 2px;">1</span>
                <span>客户信息</span>
            </div>
        </button>
        <button class="tab-button" data-tab="clothing-tab">
            <div style="display: flex; flex-direction: column; align-items: center;">
                <span style="font-size: 1.1rem; margin-bottom: 2px;">2</span>
                <span>衣物信息</span>
            </div>
        </button>
        <button class="tab-button" data-tab="payment-tab">
            <div style="display: flex; flex-direction: column; align-items: center;">
                <span style="font-size: 1.1rem; margin-bottom: 2px;">3</span>
                <span>订单确认</span>
            </div>
        </button>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
        <div class="progress" style="width: 33%;"></div>
    </div>

    <!-- 客户信息页面 -->
    <div id="customer-tab" class="tab-content active">
        <!-- 客户类型选择 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">客户类型</h3>
            </div>
            <div style="display: flex; border-radius: 8px; overflow: hidden; margin-top: 10px;">
                <div class="customer-type-option active" data-type="normal" style="flex: 1; text-align: center; padding: 12px; background: var(--background-light); cursor: pointer; font-weight: 500;">普通客户</div>
                <div class="customer-type-option" data-type="mall" style="flex: 1; text-align: center; padding: 12px; background: var(--background-light); cursor: pointer; font-weight: 500; border-left: 1px solid var(--border-color);">商场客户</div>
            </div>
        </div>

        <!-- 统一客户信息输入 -->
        <div class="card" id="normalCustomerCard">
            <div class="card-header">
                <h3 class="card-title">客户信息</h3>
            </div>
            <div class="form-group">
                <label for="customerPhone">手机号</label>
                <input type="tel" id="customerPhone" name="customerPhone" placeholder="输入手机号搜索或创建客户" required>
                <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">输入手机号自动搜索现有客户，未找到将创建新客户</div>
            </div>
            <div class="form-group">
                <label for="customerName">客户姓名</label>
                <input type="text" id="customerName" name="customerName" placeholder="请输入客户姓名" required>
            </div>
            <div class="form-group">
                <label for="address">客户地址</label>
                <input type="text" id="address" name="address" placeholder="客户地址（可选）">
            </div>
            <div class="form-group" style="text-align: center;">
                <button type="button" id="directRecharge" style="background: #10b981; width: auto; padding: 8px 16px;">账户充值</button>
            </div>
        </div>

        <!-- 客户详细信息 -->
        <div id="customerDetails" class="hidden card">
            <div class="card-header">
                <h3 class="card-title">客户信息</h3>
            </div>
            <div class="form-group">
                <label>客户姓名</label>
                <div id="customerNameDisplay" style="padding: 8px; background: #f3f4f6; border-radius: 8px; font-weight: 500;"></div>
            </div>
            <div class="form-group">
                <label>账户余额</label>
                <div style="background: #f0fdf4; border: 1px solid #bbf7d0; padding: 12px; border-radius: 8px; display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <span>当前余额:</span>
                        <span style="font-weight: bold; color: #059669; font-size: 18px; margin-left: 5px;">¥<span id="customerBalance">0.00</span></span>
                    </div>
                    <button type="button" id="openRecharge" style="background: #10b981; width: auto; padding: 6px 12px; height: 32px;">充值</button>
                </div>
            </div>
        </div>

        <!-- 商场客户选择器 -->
        <div class="mall-customer-selector card" style="display: none;">
            <div class="card-header">
                <h3 class="card-title">商场客户信息</h3>
            </div>
            <div class="form-group">
                <label for="mallCustomerName">品牌名称</label>
                <input type="text" id="mallCustomerName" name="mallCustomerName" placeholder="请输入或搜索商场品牌" required>
            </div>
            <div class="form-group">
                <label for="mallCustomerPhone">联系电话</label>
                <input type="tel" id="mallCustomerPhone" name="mallCustomerPhone" placeholder="请输入商场联系电话" required>
            </div>
        </div>

        <button type="button" class="next-step btn-secondary" id="nextToClothing">下一步: 衣物信息</button>
    </div>
    <!-- 衣物信息页面 -->
    <div id="clothing-tab" class="tab-content">
        <div id="clothingItems">
            <!-- 衣物项模板将通过JavaScript动态生成 -->
        </div>

        <button type="button" id="addClothing" style="margin-top: 15px;">添加更多衣物</button>
        <button type="button" class="next-step" id="nextToPayment">下一步: 订单确认</button>
    </div>

    <!-- 订单确认页面 -->
    <div id="payment-tab" class="tab-content">
        <!-- 客户信息卡片 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">客户信息</h3>
            </div>
            <div style="display: flex; align-items: center; gap: 10px;">
                <div style="background: var(--primary-light); color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 1.2rem;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                </div>
                <div style="flex: 1;">
                    <p style="margin-bottom: 3px;"><strong>姓名：</strong><span id="summaryName"></span></p>
                    <p style="margin: 0;"><strong>电话：</strong><span id="summaryPhone"></span></p>
                </div>
            </div>
        </div>

        <!-- 衣物清单卡片 -->
        <div class="card">
            <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                <h3 class="card-title">衣物清单</h3>
                <span style="background: var(--primary-color); padding: 5px 12px; border-radius: 15px; font-size: 0.9rem; color: white;">共 <span id="totalItems">0</span> 件</span>
            </div>
            <div id="clothingSummary" class="clothing-summary"></div>
        </div>

        <!-- 支付信息卡片 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">支付信息</h3>
            </div>

            <div style="font-size: 1.5rem; font-weight: bold; color: var(--secondary-color); text-align: right; margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
                <span style="font-size: 1rem; color: var(--text-secondary);">订单总额</span>
                <span>¥<span id="totalAmount">0.00</span></span>
            </div>

            <div class="form-group">
                <label for="paymentMethod">支付方式</label>
                <select id="paymentMethod" name="paymentMethod">
                    <option value="未付款">未付款</option>
                    <option value="扫银联码">扫银联码</option>
                    <option value="商场POS">商场POS</option>
                    <option value="余额">余额支付</option>
                </select>

                <!-- 余额支付信息 -->
                <div id="balancePaymentInfo" style="margin-top: 15px; background: #f5fff5; border: 1px solid var(--border-color); padding: 15px; border-radius: var(--card-radius); display: none;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <span>账户余额:</span>
                            <span style="font-weight: bold; color: var(--secondary-color); font-size: 1.2rem; margin-left: 5px;">¥<span id="summaryBalance">0.00</span></span>
                        </div>
                        <div id="balanceStatus" style="font-size: 0.9rem;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <button type="button" id="submitOrder" class="btn-secondary" style="padding: 16px; font-size: 1.1rem; margin-top: 20px;">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px;">
                <path d="M5 12h13M12 5l7 7-7 7"/>
            </svg>
            确认提交订单
        </button>

        <!-- 订单提交结果 -->
        <div id="orderResults" style="margin-top: 20px;"></div>
    </div>

    <!-- 衣物项模板 (隐藏) -->
    <template id="clothing-item-template">
        <div class="clothing-item">
            <button type="button" class="remove-item">×</button>

            <div class="clothing-item-header">
                <h3 class="clothing-item-title">新衣物</h3>
                <button type="button" class="toggle-collapse">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="toggle-icon">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </button>
            </div>

            <div class="clothing-content">
                <!-- 衣物基本信息 -->
                <div class="form-group">
                    <label>衣物名称</label>
                    <select class="clothing-name" name="clothingName[]" required>
                        <option value="">请选择衣物类型</option>
                        <!-- 选项将通过JavaScript动态填充 -->
                    </select>
                </div>

                <!-- 颜色、数量与价格 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>衣物颜色</label>
                        <select class="clothing-color" name="clothingColor[]">
                            <option value="白色">白色</option>
                            <option value="黑色">黑色</option>
                            <option value="灰色">灰色</option>
                            <option value="红色">红色</option>
                            <option value="蓝色">蓝色</option>
                            <option value="绿色">绿色</option>
                            <option value="黄色">黄色</option>
                            <option value="紫色">紫色</option>
                            <option value="粉色">粉色</option>
                            <option value="棕色">棕色</option>
                            <option value="米色">米色</option>
                            <option value="花色">花色</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>

                    <div class="form-group" style="flex: 0 0 100px;">
                        <label>数量</label>
                        <div class="quantity-control">
                            <button type="button" class="quantity-btn quantity-minus">-</button>
                            <input type="number" class="clothing-quantity" name="clothingQuantity[]" value="1" min="1" max="99" readonly>
                            <button type="button" class="quantity-btn quantity-plus">+</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>总价（元）</label>
                        <div class="price-input-container">
                            <input type="number" class="clothing-price" name="clothingPrice[]" value="0" min="0" step="0.01">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>瑕疵描述（可选）</label>
                    <textarea class="clothing-flaw" name="clothingFlaw[]" rows="2" placeholder="描述衣物瑕疵或损坏情况"></textarea>
                </div>

                <!-- 衣物照片 -->
                <div class="form-group">
                    <label>衣物照片</label>
                    <div class="photo-gallery">
                        <div class="photo-thumbnails"></div>
                        <button type="button" class="add-photo-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                <polyline points="21 15 16 10 5 21"></polyline>
                            </svg>
                            添加照片
                        </button>
                    </div>
                </div>

                <!-- 服务类型 -->
                <div class="form-group">
                    <label>服务类型</label>
                    <div class="service-types">
                        <label><input type="checkbox" class="service-type" name="serviceType[]" value="洗衣" checked> 洗衣</label>
                        <label><input type="checkbox" class="service-type" name="serviceType[]" value="织补"> 织补</label>
                        <label><input type="checkbox" class="service-type" name="serviceType[]" value="改衣"> 改衣</label>
                        <label><input type="checkbox" class="service-type" name="serviceType[]" value="其他"> 其他</label>
                    </div>

                    <!-- 洗衣价格 -->
                    <div class="service-requirements wash-requirements" style="display:block;">
                        <div style="display: flex; align-items: center; gap: 10px; margin-top: 10px;">
                            <label style="margin: 0;">洗衣价格：</label>
                            <div class="price-input-container" style="flex: 1;">
                                <input type="number" class="wash-price" name="washPrice[]" min="0" step="1" value="15">
                            </div>
                            <span>元</span>
                        </div>
                    </div>

                    <!-- 织补要求 -->
                    <div class="service-requirements darn-requirements" style="display:none;">
                        <label>织补要求</label>
                        <textarea class="darn-requirement" name="darnRequirement[]" rows="2" placeholder="请描述织补位置和要求" style="margin-bottom: 10px;"></textarea>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <label style="margin: 0;">织补价格：</label>
                            <div class="price-input-container" style="flex: 1;">
                                <input type="number" class="darn-price" name="darnPrice[]" min="0" step="1" value="20">
                            </div>
                            <span>元</span>
                        </div>
                    </div>

                    <!-- 改衣要求 -->
                    <div class="service-requirements alter-requirements" style="display:none;">
                        <label>改衣要求</label>
                        <textarea class="alter-requirement" name="alterRequirement[]" rows="2" placeholder="请描述改衣尺寸和要求" style="margin-bottom: 10px;"></textarea>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <label style="margin: 0;">改衣价格：</label>
                            <div class="price-input-container" style="flex: 1;">
                                <input type="number" class="alter-price" name="alterPrice[]" min="0" step="1" value="30">
                            </div>
                            <span>元</span>
                        </div>
                    </div>

                    <!-- 其他要求 -->
                    <div class="service-requirements other-requirements" style="display:none;">
                        <label>其他要求</label>
                        <textarea class="other-requirement" name="otherRequirement[]" rows="2" placeholder="请描述具体服务内容和要求" style="margin-bottom: 10px;"></textarea>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <label style="margin: 0;">其他价格：</label>
                            <div class="price-input-container" style="flex: 1;">
                                <input type="number" class="other-price" name="otherPrice[]" min="0" step="1" value="20">
                            </div>
                            <span>元</span>
                        </div>
                    </div>
                </div>

                <!-- 备注 -->
                <div class="form-group">
                    <label>备注</label>
                    <textarea class="clothing-note" name="clothingNote[]" rows="2" placeholder="其他需要记录的信息"></textarea>
                </div>
            </div>
        </div>
    </template>

    <!-- 摄像头弹窗 -->
    <div id="cameraModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.9); z-index: 1000;">
        <div style="position: relative; margin: 10% auto; padding: 20px; width: 90%; background-color: white; border-radius: 12px;">
            <span style="position: absolute; top: 10px; right: 15px; font-size: 24px; font-weight: bold; color: #333; cursor: pointer;" class="close-modal">&times;</span>
            <h3 style="margin-top: 0;">拍摄衣物照片</h3>
            <video id="modalCameraFeed" style="width: 100%; border-radius: 8px; margin-bottom: 15px;" autoplay></video>
            <canvas id="photoCanvas" style="display:none;"></canvas>
            <div style="display: flex; justify-content: space-between; gap: 15px; margin-top: 15px;">
                <button id="takePhotoBtn" type="button" style="flex: 1;">拍照</button>
                <button id="cancelBtn" type="button" style="flex: 1; background: #6c757d;">取消</button>
            </div>
        </div>
    </div>

    <!-- 处理中指示器已移至页面顶部 -->

    <!-- 充值弹窗 -->
    <div id="rechargeModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7); z-index: 1000; overflow-y: auto;">
        <div style="position: relative; margin: 10% auto; padding: 20px; width: 90%; background-color: white; border-radius: 12px;">
            <span id="closeRechargeModal" style="position: absolute; top: 10px; right: 15px; font-size: 24px; font-weight: bold; color: #333; cursor: pointer;">&times;</span>
            <h3 style="margin-top: 0;">账户充值</h3>
            <p id="rechargeUserStatus"></p>

            <div class="form-group">
                <label>选择充值金额</label>
                <div style="display: flex; flex-wrap: wrap; gap: 10px; margin: 15px 0;">
                    <div class="recharge-amount-option" data-amount="500" style="flex: 1; min-width: 80px; border: 1px solid #ddd; border-radius: 8px; padding: 12px; text-align: center; cursor: pointer;">¥500</div>
                    <div class="recharge-amount-option" data-amount="1000" style="flex: 1; min-width: 80px; border: 1px solid #ddd; border-radius: 8px; padding: 12px; text-align: center; cursor: pointer;">¥1000</div>
                    <div class="recharge-amount-option" data-amount="3000" style="flex: 1; min-width: 80px; border: 1px solid #ddd; border-radius: 8px; padding: 12px; text-align: center; cursor: pointer;">¥3000</div>
                    <div class="recharge-amount-option" data-amount="10000" style="flex: 1; min-width: 80px; border: 1px solid #ddd; border-radius: 8px; padding: 12px; text-align: center; cursor: pointer;">¥10000</div>
                </div>
                <div class="form-group" style="margin-top: 10px;">
                    <label>自定义金额</label>
                    <input type="number" id="customRechargeAmount" min="1" step="1" placeholder="输入充值金额">
                </div>
                <div class="form-group" id="giftRulesContainer" style="display: none; margin-top: 10px;">
                    <label>选择赠送规则</label>
                    <div id="giftRulesOptions">
                        <!-- 赠送规则选项将在这里动态加载 -->
                    </div>
                </div>
                <div class="form-group" style="margin-top: 10px;">
                    <label>预计赠送金额</label>
                    <div id="giftAmountDisplay" style="color: #28a745; font-weight: bold; font-size: 1.1em; padding: 8px; background: #f8f9fa; border-radius: 6px;">¥0.00</div>
                </div>
            </div>

            <div class="form-group">
                <label>选择支付方式</label>
                <div style="display: flex; gap: 15px; margin: 15px 0;">
                    <div class="recharge-payment-method" data-method="扫银联码" style="flex: 1; padding: 12px; text-align: center; border: 1px solid #ddd; border-radius: 8px; cursor: pointer;">扫银联码</div>
                    <div class="recharge-payment-method" data-method="商场POS" style="flex: 1; padding: 12px; text-align: center; border: 1px solid #ddd; border-radius: 8px; cursor: pointer;">商场POS</div>
                </div>
            </div>

            <button type="button" id="confirmRecharge" style="width: 100%; padding: 14px; margin-top: 15px; background: #28a745;">确认充值</button>
        </div>
    </div>

    <!-- 底部菜单 -->
    <div class="mobile-menu">
        <a href="/" class="menu-item active">
            <div class="menu-icon">🏠</div>
            <span>首页</span>
        </a>
        <a href="/history" class="menu-item">
            <div class="menu-icon">📋</div>
            <span>历史订单</span>
        </a>
        <a href="#" class="menu-item" id="menuMore">
            <div class="menu-icon">⋯</div>
            <span>更多</span>
        </a>
    </div>

    <!-- 添加衣物按钮 -->
    <div class="action-button" id="floatingAddButton" style="display: none;">+</div>

    <!-- 小票打印弹窗 -->
    <div id="receiptModal" style="display: none; position: fixed; z-index: 2000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="position: relative; background-color: #fff; margin: 10% auto; padding: 20px; width: 90%; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
            <div style="display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ddd; padding-bottom: 10px; margin-bottom: 15px;">
                <h3 style="margin: 0;">小票打印预览</h3>
                <button class="close-print-modal" style="font-size: 24px; font-weight: bold; background: none; border: none; cursor: pointer;">&times;</button>
            </div>
            <div id="receiptContent" style="max-height: 60vh; overflow-y: auto; margin-bottom: 15px;"></div>
            <div style="display: flex; justify-content: flex-end; border-top: 1px solid #ddd; padding-top: 15px;">
                <button id="printReceiptBtn" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 8px; cursor: pointer;">打印小票</button>
            </div>
        </div>
    </div>

    <!-- 水洗唛打印弹窗 -->
    <div id="labelModal" style="display: none; position: fixed; z-index: 2000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="position: relative; background-color: #fff; margin: 10% auto; padding: 20px; width: 90%; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
            <div style="display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ddd; padding-bottom: 10px; margin-bottom: 15px;">
                <h3 style="margin: 0;">水洗唛打印预览</h3>
                <button class="close-print-modal" style="font-size: 24px; font-weight: bold; background: none; border: none; cursor: pointer;">&times;</button>
            </div>
            <div style="margin-bottom: 15px;">
                <div style="margin-bottom: 10px;">
                    <span style="font-weight: bold; display: block; margin-bottom: 5px;">选择要打印的水洗唛：</span>
                    <select id="labelSelect" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 15px;">
                        <option value="all">打印所有水洗唛</option>
                    </select>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button id="printAllLabelsBtn" style="flex: 1; padding: 10px; background-color: #007bff; color: white; border: none; border-radius: 8px; cursor: pointer;">打印全部</button>
                    <button id="printSelectedLabelBtn" style="flex: 1; padding: 10px; background-color: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer;">打印选中项</button>
                </div>
            </div>
            <div id="labelContent" style="max-height: 50vh; overflow-y: auto; margin-bottom: 15px;"></div>
        </div>
    </div>

    <style>
    /* 订单确认页样式 */
    .clothing-summary {
        margin-top: 10px;
    }

    .summary-item {
        background: var(--background-light);
        border-radius: 8px;
        padding: 10px 12px;
        margin-bottom: 8px;
    }

    .summary-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        margin-bottom: 5px;
        color: var(--text-primary);
    }

    .summary-item-content {
        font-size: 0.85rem;
        color: var(--text-secondary);
    }

    .summary-item-price {
        font-weight: bold;
        color: var(--secondary-color);
    }

    /* 数量控件样式 */
    .quantity-control {
        display: flex;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 6px;
        overflow: hidden;
        height: 40px;
    }
    .quantity-btn {
        width: 32px;
        height: 40px;
        border: none;
        background: #f8f9fa;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: #495057;
        transition: background-color 0.2s;
        font-size: 16px;
    }
    .quantity-btn:hover {
        background: #e9ecef;
    }
    .quantity-btn:active {
        background: #dee2e6;
    }
    .clothing-quantity {
        width: 36px;
        height: 40px;
        border: none;
        text-align: center;
        font-weight: bold;
        background: white;
        outline: none;
        font-size: 14px;
    }
    .clothing-quantity:focus {
        background: #f8f9fa;
    }
    </style>

    <script src="/static/js/clothing-options.js"></script>
    <script src="/static/js/print-functions.js"></script>
    <script src="/static/js/mobile-functions.js"></script>

    <script>
        // 移动端折扣功能集成
        document.addEventListener('DOMContentLoaded', async function() {
            // 从数据库加载服务价格
            try {
                await loadServicePricesFromDatabase();
                console.log("移动端服务价格加载完成:", defaultServicePrices);
            } catch (error) {
                console.warn("移动端服务价格加载失败，使用默认价格:", error);
            }

            // 监听衣物名称选择变化，应用折扣
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('clothing-name')) {
                    const itemElement = e.target.closest('.clothing-item');
                    if (itemElement && window.currentMallCustomer) {
                        // 延迟执行以确保其他事件处理完成
                        setTimeout(() => {
                            applyDiscountToItemMobile(itemElement);
                        }, 100);
                    }
                }
            });
        });
    </script>
</body>
</html>
