# 服务类型价格模型重构说明

## 重构概述

本次重构解决了原有价格计算逻辑的问题，将洗衣、织补、改衣三种服务的价格独立管理，提供更直观和准确的价格计算方式。

## 主要问题

### 原有问题
1. **逻辑混乱**：将"基础价格"等同于"洗衣价格"
2. **反向计算**：洗衣价格通过总价减去其他服务价格计算，可能导致负数
3. **无法独立修改**：洗衣服务价格无法单独设置
4. **不够直观**：价格计算方式不符合用户直觉

### 解决方案
1. **独立价格管理**：每种服务都有独立的价格输入控件
2. **累加计算**：总价格 = 洗衣价格 + 织补价格 + 改衣价格
3. **直观操作**：用户可以直接设置每种服务的价格
4. **向后兼容**：确保现有订单数据正常显示

## 修改内容

### 1. 默认价格配置
```javascript
// static/js/clothing-options.js
const defaultServicePrices = {
    '洗衣': 15,  // 新增洗衣默认价格
    '织补': 20,
    '改衣': 30
};
```

### 2. 用户界面修改

#### 桌面版 (templates/index.html)
- 添加洗衣价格输入控件
- 修改价格计算逻辑为累加模式
- 更新服务类型变更事件处理

#### 移动端 (templates/mobile_index.html)
- 添加洗衣价格输入控件
- 同步桌面版的价格计算逻辑

### 3. JavaScript 逻辑修改

#### 桌面版价格计算
```javascript
function updateTotalPrice() {
    let totalPrice = 0;

    // 添加洗衣价格
    if (serviceCheckboxes[0].checked && washPriceInput) {
        totalPrice += parseFloat(washPriceInput.value || 0);
    }

    // 添加织补价格
    if (serviceCheckboxes[1].checked && darnPriceInput) {
        totalPrice += parseFloat(darnPriceInput.value || 0);
    }

    // 添加改衣价格
    if (serviceCheckboxes[2].checked && alterPriceInput) {
        totalPrice += parseFloat(alterPriceInput.value || 0);
    }

    priceInput.value = totalPrice.toFixed(2);
}
```

#### 移动端价格计算
```javascript
function updateItemPrice(itemElement) {
    const serviceCheckboxes = itemElement.querySelectorAll('.service-type:checked');
    let totalPrice = 0;

    serviceCheckboxes.forEach(checkbox => {
        const serviceType = checkbox.value;
        if (serviceType === '洗衣' && washPriceInput) {
            totalPrice += parseFloat(washPriceInput.value || 0);
        } else if (serviceType === '织补' && darnPriceInput) {
            totalPrice += parseFloat(darnPriceInput.value || 0);
        } else if (serviceType === '改衣' && alterPriceInput) {
            totalPrice += parseFloat(alterPriceInput.value || 0);
        }
    });

    if (totalPriceDisplay) {
        totalPriceDisplay.value = totalPrice.toFixed(2);
    }
}
```

### 4. 打印功能更新

#### 水洗唛价格显示 (static/js/print-functions.js)
- 修改价格获取逻辑，从特殊要求中获取各服务独立价格
- 移除反向计算逻辑
- 确保价格显示准确

#### 小票打印
- 保持现有格式，显示服务类型组合
- 总价格仍为所有服务价格之和

### 5. 数据存储结构

#### 特殊要求数据结构
```javascript
specialRequirements: {
    wash: {
        requirement: '',
        price: 15  // 洗衣价格
    },
    darn: {
        requirement: '织补要求描述',
        price: 20  // 织补价格
    },
    alter: {
        requirement: '改衣要求描述',
        price: 30  // 改衣价格
    }
}
```

## 测试要点

### 功能测试
1. **价格计算**：验证选择不同服务组合时价格计算正确
2. **界面响应**：确认服务类型选择时界面正确显示/隐藏价格输入
3. **数据保存**：验证订单提交时价格数据正确保存
4. **打印功能**：确认小票和水洗唛价格显示正确

### 兼容性测试
1. **现有订单**：确保历史订单数据正常显示
2. **移动端**：验证移动端功能与桌面端一致
3. **打印输出**：确认打印格式和内容正确

### 边界测试
1. **价格为0**：测试某服务价格为0时的处理
2. **未选择服务**：测试未选择任何服务时的处理
3. **价格输入验证**：测试负数和非数字输入的处理

## 优势

1. **逻辑清晰**：每种服务价格独立管理，计算逻辑直观
2. **用户友好**：可以直接设置每种服务的价格
3. **避免错误**：消除了洗衣价格为负数的可能性
4. **易于维护**：代码结构更清晰，便于后续维护和扩展
5. **向后兼容**：不影响现有订单数据的显示和处理

## 注意事项

1. **默认价格**：新添加衣物时会自动设置默认的洗衣价格
2. **价格同步**：修改服务价格时会自动更新总价格
3. **数据一致性**：确保前端显示与后端存储的价格数据一致
4. **打印格式**：水洗唛会显示每种服务的具体价格

## 向后兼容性处理

### 历史订单显示
为了确保现有订单数据正常显示，我们在历史订单页面实现了向后兼容逻辑：

```javascript
// 获取洗衣价格
if (item.requirements.wash && item.requirements.wash.price) {
    washingPrice = parseFloat(item.requirements.wash.price);
} else if (item.services.includes('洗衣')) {
    // 向后兼容：如果没有独立的洗衣价格，使用旧的计算方式
    washingPrice = item.price - darningPrice - alteringPrice;
    washingPrice = Math.max(0, washingPrice);
}
```

这样确保：
1. 新订单使用新的独立价格模型
2. 旧订单仍然使用原有的计算方式正确显示
3. 不会出现数据显示错误或价格为负数的情况

## 完成的修改清单

### ✅ 已完成的修改

1. **默认价格配置** - `static/js/clothing-options.js`
   - 添加洗衣默认价格：15元

2. **桌面版界面** - `templates/index.html`
   - 添加洗衣价格输入控件
   - 修改价格计算逻辑为累加模式
   - 更新服务类型变更事件处理
   - 修改数据初始化结构

3. **移动端界面** - `templates/mobile_index.html`
   - 添加洗衣价格输入控件
   - 同步桌面版的价格计算逻辑

4. **移动端JavaScript** - `static/js/mobile-functions.js`
   - 添加价格计算函数
   - 更新服务类型处理逻辑
   - 修改订单提交数据结构

5. **打印功能** - `static/js/print-functions.js`
   - 更新水洗唛价格显示逻辑
   - 支持独立的洗衣价格显示

6. **历史订单页面** - `templates/history.html`
   - 添加向后兼容的价格计算逻辑
   - 更新订单详情显示
   - 修改编辑功能的价格处理

## 测试建议

### 新订单测试
1. 创建新订单，选择不同服务组合
2. 验证价格计算是否正确
3. 测试打印功能（小票和水洗唛）
4. 验证移动端和桌面端功能一致性

### 历史订单测试
1. 查看现有历史订单，确认价格显示正确
2. 编辑历史订单，验证价格计算逻辑
3. 打印历史订单，确认格式正确

### 边界情况测试
1. 价格为0的服务
2. 只选择单一服务
3. 取消所有服务选择
4. 输入非法价格值
