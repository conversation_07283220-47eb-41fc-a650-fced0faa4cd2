# Soulweave改衣坊管理系统

一个专为洗衣店、手工坊等服务行业设计的全功能管理系统，支持客户管理、订单处理、商场客户管理、会员管理等核心业务功能。

## ✨ 主要功能

### 🏪 核心业务功能
- **客户信息管理** - 客户注册、查询、余额管理
- **衣物信息采集** - 拍照上传、类型选择、瑕疵记录
- **订单创建和管理** - 完整的订单生命周期管理
- **订单状态流转** - 从分拣到配送的全流程跟踪
- **条形码生成与打印** - 支持水洗唛标签打印

### 💰 财务管理
- **客户余额管理** - 充值、消费、余额查询
- **充值赠送系统** - 灵活的充值优惠规则
- **多种支付方式** - 现金、扫码、POS机、余额支付
- **商场客户月结** - 商场客户专用的月度账单系统

### 👥 客户管理
- **普通客户管理** - 个人客户信息维护
- **商场客户管理** - 企业客户、折扣设置、账单管理
- **会员管理** - 会员等级、专属折扣、服务优惠

### 📊 数据分析
- **订单历史查询** - 多维度订单检索
- **数据统计报表** - 营业额分析、客户消费统计
- **Excel数据导出** - 支持账单、订单数据导出

### 🔐 系统管理
- **用户权限管理** - 多角色权限控制
- **产品管理** - 服务类型、价格配置
- **移动端适配** - 响应式设计，支持手机操作

## 🛠 技术栈

### 后端技术
- **Web框架**: Flask 2.3.2
- **数据库ORM**: Flask-SQLAlchemy 3.0.3
- **数据库**: MySQL
- **图像处理**: Pillow 9.5.0
- **条形码生成**: python-barcode 0.15.1
- **密码加密**: Werkzeug 2.3.6

### 前端技术
- **基础技术**: HTML5, CSS3, JavaScript (ES6+)
- **UI框架**: Bootstrap 5
- **图表库**: Chart.js
- **条码扫描**: QuaggaJS
- **移动端**: 响应式设计，PWA支持

### 数据库
- **主数据库**: MySQL 8.0+
- **连接驱动**: PyMySQL 1.1.0

## 🚀 快速开始

### 📋 系统要求

- **Python**: 3.8+
- **MySQL**: 8.0+ (推荐) 或 5.7+
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+

### 1️⃣ 环境准备

```bash
# 克隆项目
git clone <repository_url>
cd 快速收衣

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate
```

### 2️⃣ 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 如果安装速度慢，可以使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 3️⃣ 数据库配置

#### 创建MySQL数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE laundry_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'laundry_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON laundry_system.* TO 'laundry_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env
```

编辑 `.env` 文件：
```bash
# 数据库连接配置
DB_TYPE=mysql
DB_USERNAME=laundry_user
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=laundry_system

# 应用配置
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=your_secret_key_here_change_in_production

# 上传文件配置
UPLOAD_FOLDER=static/uploads
MAX_CONTENT_LENGTH=16777216  # 16MB
```

### 4️⃣ 初始化数据库

```bash
# 运行数据库初始化脚本
python init_db.py

# 如果需要更新数据库结构
python update_db.py
```

### 5️⃣ 启动应用

```bash
# 开发模式启动
python app.py

# 或者使用Flask命令
flask run --host=0.0.0.0 --port=5000
```

🎉 **访问应用**: http://localhost:5000

### 6️⃣ 默认账号

系统会自动创建默认管理员账号：
- **用户名**: admin
- **密码**: admin123
- **角色**: 系统管理员

> ⚠️ **安全提醒**: 首次登录后请立即修改默认密码！

## 📖 功能详细说明

### 🏪 订单管理流程

#### 1. 客户信息管理
- **快速查询**: 通过手机号一键查询客户信息
- **新客户注册**: 简单快捷的客户信息录入
- **余额管理**: 实时查看客户余额、充值记录
- **会员等级**: 支持不同会员等级和专属折扣

#### 2. 衣物信息采集
- **多件衣物**: 支持一次性添加多件衣物
- **拍照上传**: 高质量图片压缩和存储
- **详细分类**: 丰富的衣物类型和颜色选择
- **服务选择**: 洗衣、织补、改衣、其他服务
- **特殊要求**: 详细的服务要求记录
- **瑕疵记录**: 衣物瑕疵拍照和文字记录

#### 3. 订单处理
- **智能定价**: 根据服务类型自动计算价格
- **折扣系统**: 支持会员折扣、商场客户折扣
- **多种支付**: 现金、扫码、POS机、余额支付
- **订单摘要**: 清晰的订单信息展示
- **条码打印**: 自动生成水洗唛条码标签

#### 4. 状态跟踪
- **全流程跟踪**: 从分拣到配送的完整状态管理
- **批量更新**: 支持批量状态更新操作
- **扫码更新**: 通过扫描条码快速更新状态
- **历史记录**: 完整的状态变更日志

### 💰 财务管理系统

#### 5. 客户余额系统
- **灵活充值**: 支持多种充值方式
- **赠送规则**: 可配置的充值赠送策略
- **余额支付**: 优先使用赠送余额
- **充值记录**: 详细的充值和消费记录

#### 6. 商场客户管理
- **企业客户**: 专门的商场客户管理模块
- **产品折扣**: 灵活的产品级别折扣设置
- **月度账单**: 自动生成月度账单和统计
- **Excel导出**: 支持账单数据导出

### 👥 用户和权限

#### 7. 会员管理
- **会员信息**: 完整的会员档案管理
- **专属折扣**: 会员专享的服务折扣
- **充值优惠**: 会员专属充值赠送规则
- **消费统计**: 会员消费行为分析

#### 8. 用户权限管理
- **多角色支持**: 管理员、店员等不同角色
- **权限控制**: 细粒度的功能权限管理
- **区域管理**: 支持多店面、多区域管理
- **操作日志**: 完整的用户操作记录

### 📊 数据分析

#### 9. 统计报表
- **实时统计**: 订单数量、营业额实时统计
- **趋势分析**: 业务数据趋势图表展示
- **客户分析**: 客户消费行为和偏好分析
- **导出功能**: 支持Excel格式数据导出

## 📁 项目结构

```
快速收衣/
├── 📄 核心文件
│   ├── app.py                    # 🚀 Flask主应用文件
│   ├── config.py                 # ⚙️ 应用配置管理
│   ├── models.py                 # 🗄️ 数据库模型定义
│   ├── utils.py                  # 🛠️ 工具函数库
│   └── requirements.txt          # 📦 Python依赖列表
│
├── 🗄️ 数据库相关
│   ├── init_db.py               # 🔧 数据库初始化脚本
│   ├── update_db.py             # 🔄 数据库结构更新
│   ├── update_db_mall.py        # 🏢 商场客户数据更新
│   └── update_db_product.py     # 📦 产品数据更新
│
├── 🌐 前端资源
│   ├── static/                  # 静态资源目录
│   │   ├── css/                 # 样式文件
│   │   │   ├── print-styles.css # 打印样式
│   │   │   └── mobile-styles.css # 移动端样式
│   │   ├── js/                  # JavaScript文件
│   │   │   ├── clothing-options.js    # 衣物选项配置
│   │   │   ├── discount-functions.js  # 折扣计算函数
│   │   │   ├── image-compressor.js    # 图片压缩工具
│   │   │   ├── mobile-functions.js    # 移动端功能
│   │   │   └── print-functions.js     # 打印功能
│   │   └── uploads/             # 用户上传的图片
│   │
│   └── templates/               # HTML模板文件
│       ├── index.html           # 🏠 主页/订单录入
│       ├── mobile_index.html    # 📱 移动端主页
│       ├── login.html           # 🔐 用户登录页面
│       ├── history.html         # 📋 历史订单查询
│       ├── status_update.html   # 🔄 订单状态更新
│       ├── data_summary.html    # 📊 数据统计报表
│       ├── mall_customer_management.html # 🏢 商场客户管理
│       ├── member_management.html       # 👥 会员管理
│       ├── product_management.html      # 📦 产品管理
│       ├── user_management.html         # 👤 用户权限管理
│       ├── receipt1.html        # 🧾 小票打印模板
│       ├── labels.html          # 🏷️ 标签打印页面
│       └── sticky_label_print.html # 🏷️ 水洗唛打印
│
├── 📚 文档和配置
│   ├── README.md                # 📖 项目说明文档
│   ├── .env.example            # 🔧 环境变量模板
│   ├── .env                    # 🔐 环境变量配置（需创建）
│   └── docs/                   # 📚 项目文档目录
│       └── 打印功能维护指南.md    # 🖨️ 打印功能说明
│
└── 🧪 测试和工具
    └── 条码测试.py               # 🔍 条形码功能测试
```

## 🔧 开发者指南

### 🗄️ 数据库模型

系统采用关系型数据库设计，主要包含以下核心模型：

#### 👥 用户和客户模型
- **Customer** - 普通客户信息（姓名、手机、余额、会员等级）
- **Staff** - 员工信息（用户名、密码、角色、区域）
- **MallCustomer** - 商场客户信息（企业客户、合同信息）

#### 📦 订单和商品模型
- **Order** - 订单主表（订单号、金额、状态、支付方式）
- **Clothing** - 衣物详情（名称、颜色、服务类型、价格）
- **ClothingPhoto** - 衣物照片（图片路径、关联衣物）
- **Product** - 产品定义（服务类型、基础价格）

#### 💰 财务模型
- **RechargeRecord** - 充值记录（充值金额、赠送金额、支付方式）
- **RechargeGiftRule** - 充值赠送规则（最小金额、赠送比例）
- **MemberServiceDiscount** - 会员服务折扣（服务类型、折扣率）

#### 🏢 商场客户模型
- **MallProductDiscount** - 商场产品折扣（产品级别折扣）
- **MallMonthlyBill** - 商场月度账单（账期、金额统计）
- **MallDiscountHistory** - 折扣变更历史（审计日志）

#### 📊 日志模型
- **OrderStatusLog** - 订单状态变更日志（状态变更记录）

### 🔌 API接口文档

#### 🔐 认证相关
```http
POST /login              # 用户登录
GET  /logout             # 用户登出
```

#### 👥 客户管理
```http
GET  /search_customer    # 查询客户信息
POST /recharge_account   # 客户账户充值
GET  /api/members        # 获取会员列表
POST /api/members        # 创建新会员
PUT  /api/members/<id>   # 更新会员信息
POST /api/members/<id>/recharge  # 会员充值
GET  /api/members/<id>/orders    # 会员订单查询
```

#### 📦 订单管理
```http
POST /submit_order       # 提交新订单
GET  /customer_history   # 查询历史订单
GET  /order_details      # 订单详情查询
POST /update_order_status       # 更新订单状态
POST /batch_update_order_status # 批量更新状态
POST /update_status_by_number   # 通过编号更新状态
PUT  /api/orders/<id>/clothing/<clothing_id>  # 更新衣物信息
```

#### 🏷️ 条形码和打印
```http
GET  /barcode/<order_number>/<index>  # 生成条形码图片
GET  /receipt/<order_id>              # 获取小票内容
GET  /labels                          # 标签打印页面
```

#### 🏢 商场客户管理
```http
GET  /api/mall_customers              # 获取商场客户列表
POST /api/mall_customers              # 创建商场客户
PUT  /api/mall_customers/<id>         # 更新商场客户
GET  /api/mall_customers/<id>/discounts    # 获取客户折扣
POST /api/mall_customers/<id>/discounts    # 设置客户折扣
GET  /api/mall_customers/<id>/bills        # 获取客户账单
POST /api/bills/<id>/generate              # 生成月度账单
GET  /api/bills/<id>/orders               # 获取账单订单明细
GET  /api/bills/<id>/export               # 导出账单Excel
```

#### 📦 产品管理
```http
GET  /api/products       # 获取产品列表
POST /api/products       # 创建新产品
PUT  /api/products/<id>  # 更新产品信息
DELETE /api/products/<id> # 删除产品
GET  /api/service_prices # 获取服务价格配置
```

#### 👤 用户管理
```http
GET  /api/users          # 获取用户列表
POST /api/users          # 创建新用户
PUT  /api/users/<id>     # 更新用户信息
DELETE /api/users/<id>   # 删除用户
GET  /api/areas          # 获取区域列表
```

#### 📊 数据统计
```http
GET  /api/summary_data   # 获取统计数据
GET  /api/status_logs    # 获取状态变更日志
```

## 🚀 部署指南

### 生产环境部署

#### 1. 服务器要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上可用空间
- **网络**: 稳定的网络连接

#### 2. 生产环境配置
```bash
# 设置生产环境变量
export FLASK_ENV=production
export FLASK_DEBUG=0

# 使用更安全的密钥
export SECRET_KEY=your_very_secure_secret_key_here

# 配置数据库连接池
export DB_POOL_SIZE=10
export DB_MAX_OVERFLOW=20
```

#### 3. 使用Gunicorn部署
```bash
# 安装Gunicorn
pip install gunicorn

# 启动应用
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

#### 4. 使用Nginx反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /static {
        alias /path/to/your/app/static;
        expires 30d;
    }
}
```

## 🔧 常见问题

### Q: 条形码生成失败怎么办？
A: 确保已安装 `python-barcode` 和 `Pillow` 库：
```bash
pip install python-barcode Pillow
```

### Q: 图片上传失败？
A: 检查 `static/uploads` 目录权限，确保应用有写入权限。

### Q: 数据库连接失败？
A: 检查 `.env` 文件中的数据库配置，确保MySQL服务正在运行。

### Q: 移动端显示异常？
A: 清除浏览器缓存，确保加载了最新的CSS和JS文件。

## 🤝 贡献指南

### 开发流程
1. Fork 项目到你的GitHub账号
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交你的修改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范
- 遵循PEP 8 Python代码规范
- 使用有意义的变量和函数名
- 添加必要的注释和文档字符串
- 确保新功能有相应的测试

## 📞 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 📧 **邮件支持**: 发送详细问题描述到技术支持邮箱
- 📱 **在线文档**: 查看项目Wiki获取更多技术细节
- 🐛 **问题反馈**: 在GitHub Issues中提交bug报告
- 💡 **功能建议**: 欢迎提交新功能建议和改进意见

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**Soulweave改衣坊管理系统** - 让服务行业管理更简单、更高效！ 🎉