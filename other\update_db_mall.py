from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import pymysql
import sys
from sqlalchemy import text

# 将PyMySQL注册为MySQLdb
pymysql.install_as_MySQLdb()

app = Flask(__name__)

# 数据库配置
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://ytgf:ZEzaJikm2kNakFbk@49.232.0.106:3306/ytgf'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

def add_columns():
    """为现有数据表添加商场客户相关字段"""
    with app.app_context():
        try:
            # 向Customer表添加新字段
            print("正在向Customer表添加商场客户相关字段...")
            db.session.execute(text("""
                ALTER TABLE customer 
                ADD COLUMN is_mall_customer BOOLEAN DEFAULT FALSE,
                ADD COLUMN mall_customer_id INT NULL,
                ADD CONSTRAINT fk_customer_mall_customer 
                FOREIGN KEY (mall_customer_id) REFERENCES mall_customer(id)
                ON DELETE SET NULL;
            """))
            
            # 向Order表添加新字段
            print("正在向Order表添加商场客户订单相关字段...")
            db.session.execute(text("""
                ALTER TABLE `order` 
                ADD COLUMN is_mall_order BOOLEAN DEFAULT FALSE,
                ADD COLUMN mall_customer_id INT NULL,
                ADD COLUMN discount_amount FLOAT DEFAULT 0.0,
                ADD CONSTRAINT fk_order_mall_customer 
                FOREIGN KEY (mall_customer_id) REFERENCES mall_customer(id)
                ON DELETE SET NULL;
            """))
            
            # 向Clothing表添加新字段
            print("正在向Clothing表添加折扣相关字段...")
            db.session.execute(text("""
                ALTER TABLE clothing 
                ADD COLUMN is_mall_order BOOLEAN DEFAULT FALSE,
                ADD COLUMN original_price FLOAT NULL,
                ADD COLUMN discount_rate FLOAT NULL;
            """))
            
            db.session.commit()
            print("数据库更新成功！新字段已添加到现有表中。")
            return True
        
        except Exception as e:
            db.session.rollback()
            print(f"数据库更新失败: {str(e)}")
            return False

if __name__ == "__main__":
    print("开始更新数据库结构...")
    success = add_columns()
    
    if success:
        print("数据库结构更新成功!")
        sys.exit(0)
    else:
        print("数据库结构更新失败，请检查错误信息。")
        sys.exit(1)
