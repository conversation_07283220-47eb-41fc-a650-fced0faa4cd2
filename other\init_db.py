import pymysql
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

DB_USERNAME = os.environ.get('DB_USERNAME', 'root')
DB_PASSWORD = os.environ.get('DB_PASSWORD', '709426tzb')
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_PORT = int(os.environ.get('DB_PORT', '3306'))
DB_NAME = os.environ.get('DB_NAME', 'laundry_system')

def create_database():
    """创建数据库"""
    try:
        # 连接到MySQL服务器
        print(f"正在连接到MySQL服务器 {DB_HOST}:{DB_PORT}...")
        conn = pymysql.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USERNAME,
            password=DB_PASSWORD
        )
        
        cursor = conn.cursor()
        
        # 创建数据库
        print(f"正在创建数据库 {DB_NAME}...")
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DB_NAME} DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        
        print("数据库创建成功!")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        print(f"创建数据库时出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("======= Soulweave改衣坊数据库初始化 =======")
    if create_database():
        print("\n现在创建数据表: python app.py")
        print("应用将自动创建所需的数据表并启动服务。") 