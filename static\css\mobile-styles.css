/**
 * Soulweave改衣坊移动端UI系统
 * 现代化、专业的服务管理界面设计
 */

/* 重构后的设计系统 */
:root {
  /* 主色彩系统 - 温暖专业的羊驼品牌色 */
  --primary-color: #6366f1; /* 现代紫蓝色 - 专业可信 */
  --primary-light: #818cf8;
  --primary-dark: #4f46e5;
  --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);

  /* 功能色彩 */
  --success-color: #10b981; /* 清新绿色 - 成功状态 */
  --success-light: #34d399;
  --warning-color: #f59e0b; /* 温暖橙色 - 警告提示 */
  --danger-color: #ef4444; /* 现代红色 - 错误危险 */
  --info-color: #3b82f6; /* 信息蓝色 */

  /* 中性色系 */
  --text-primary: #1f2937; /* 深灰 - 主要文字 */
  --text-secondary: #6b7280; /* 中灰 - 次要文字 */
  --text-tertiary: #9ca3af; /* 浅灰 - 辅助文字 */
  --text-inverse: #ffffff; /* 反色文字 */

  /* 背景色系 */
  --bg-primary: #ffffff; /* 主背景 */
  --bg-secondary: #f9fafb; /* 次要背景 */
  --bg-tertiary: #f3f4f6; /* 第三背景 */
  --bg-overlay: rgba(0, 0, 0, 0.5); /* 遮罩背景 */

  /* 边框色系 */
  --border-light: #e5e7eb; /* 浅边框 */
  --border-medium: #d1d5db; /* 中等边框 */
  --border-dark: #9ca3af; /* 深边框 */

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 圆角系统 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;

  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;

  /* 字体系统 */
  --font-xs: 12px;
  --font-sm: 14px;
  --font-base: 16px;
  --font-lg: 18px;
  --font-xl: 20px;
  --font-2xl: 24px;

  /* 行高系统 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
}

/* 现代化动画系统 */
@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(8px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
    from { transform: translateY(24px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(-24px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
    from { transform: scale(0.95); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0, -8px, 0); }
    70% { transform: translate3d(0, -4px, 0); }
    90% { transform: translate3d(0, -2px, 0); }
}

/* 现代化骨架屏 */
.skeleton-loader {
    background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--border-light) 50%, var(--bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-md);
    height: 20px;
    margin-bottom: var(--space-sm);
}

.skeleton-line.short { width: 40%; }
.skeleton-line.medium { width: 70%; }
.skeleton-line.long { width: 100%; }

/* 现代化移动端基础样式 */
@media (max-width: 768px) {
    /* 重置和基础样式 */
    * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
    }

    body {
        margin: 0;
        padding: 0;
        padding-bottom: 72px; /* 为底部导航留出空间 */
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        font-size: 14px; /* 紧凑化字体 */
        line-height: 1.4; /* 紧凑化行高 */
        color: var(--text-primary);
        background-color: var(--bg-secondary);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        overflow-x: hidden;
    }

    /* 现代化标题系统 */
    h1, h2, h3, h4, h5, h6 {
        margin: 0;
        font-weight: 600;
        line-height: var(--leading-tight);
        color: var(--text-primary);
        letter-spacing: -0.025em;
    }

    h1 {
        font-size: var(--font-2xl);
        font-weight: 700;
    }
    h2 {
        font-size: var(--font-xl);
        font-weight: 600;
    }
    h3 {
        font-size: var(--font-lg);
        font-weight: 600;
    }
    h4 {
        font-size: var(--font-base);
        font-weight: 600;
    }

    p {
        margin: 0;
        line-height: var(--leading-normal);
        color: var(--text-secondary);
    }

    /* 链接样式 */
    a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.2s ease;
    }

    a:hover, a:focus {
        color: var(--primary-dark);
    }

    /* 现代化表单系统 - 紧凑化 */
    input, select, textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        width: 100%;
        font-size: 14px; /* 紧凑化字体 */
        font-family: inherit;
        padding: var(--space-sm) var(--space-md); /* 减少内边距 */
        border: 1px solid var(--border-light); /* 减小边框 */
        border-radius: var(--radius-md); /* 减小圆角 */
        background-color: var(--bg-primary);
        color: var(--text-primary);
        transition: all 0.2s ease;
        box-sizing: border-box;
        min-height: 36px; /* 紧凑化高度 */
    }

    input:focus, select:focus, textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background-color: var(--bg-primary);
    }

    input:disabled, select:disabled, textarea:disabled {
        background-color: var(--bg-tertiary);
        color: var(--text-tertiary);
        cursor: not-allowed;
    }

    /* 占位符样式 */
    input::placeholder, textarea::placeholder {
        color: var(--text-tertiary);
        opacity: 1;
    }

    /* 表单行布局 */
    .form-row {
        display: flex;
        gap: var(--space-md);
        margin-bottom: var(--space-md);
    }

    .form-row .form-group {
        flex: 1;
        min-width: 0;
    }

    /* 文本区域 */
    textarea {
        min-height: 96px;
        resize: vertical;
        line-height: var(--leading-normal);
    }

    /* 选择框特殊样式 */
    select {
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right var(--space-md) center;
        background-size: 20px;
        padding-right: var(--space-2xl);
    }

    /* 现代化按钮系统 */
    button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-xs); /* 减少间距 */
        padding: var(--space-sm) var(--space-md); /* 减少内边距 */
        font-size: 14px; /* 紧凑化字体 */
        font-weight: 500; /* 减轻字重 */
        font-family: inherit;
        line-height: 1;
        border: none;
        border-radius: var(--radius-md); /* 减小圆角 */
        background: var(--primary-gradient);
        color: var(--text-inverse);
        cursor: pointer;
        transition: all 0.2s ease;
        min-height: 36px; /* 紧凑化高度 */
        box-shadow: var(--shadow-sm);
        position: relative;
        overflow: hidden;
        width: 100%;
    }

    button:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    button:active {
        transform: translateY(0);
        box-shadow: var(--shadow-sm);
    }

    button:disabled {
        background: var(--bg-tertiary);
        color: var(--text-tertiary);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    /* 按钮变种 */
    button.btn-secondary {
        background: var(--success-color);
        color: var(--text-inverse);
    }

    button.btn-secondary:hover {
        background: var(--success-light);
    }

    button.btn-outline {
        background: transparent;
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        box-shadow: none;
    }

    button.btn-outline:hover {
        background: var(--primary-color);
        color: var(--text-inverse);
    }

    button.btn-danger {
        background: var(--danger-color);
        color: var(--text-inverse);
    }

    button.btn-ghost {
        background: transparent;
        color: var(--text-secondary);
        box-shadow: none;
    }

    button.btn-ghost:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    /* 按钮尺寸 */
    button.btn-sm {
        padding: var(--space-sm) var(--space-md);
        font-size: var(--font-sm);
        min-height: 36px;
    }

    button.btn-lg {
        padding: var(--space-lg) var(--space-xl);
        font-size: var(--font-lg);
        min-height: 56px;
    }

    /* 按钮组 */
    .btn-group {
        display: flex;
        gap: var(--space-sm);
    }

    .btn-group button {
        flex: 1;
    }

    button.btn-outline {
        background: transparent;
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        box-shadow: none;
    }

    button.btn-outline:hover {
        background: var(--primary-color);
        color: var(--text-inverse);
    }

    button.btn-danger {
        background: var(--danger-color);
        color: var(--text-inverse);
    }

    button.btn-ghost {
        background: transparent;
        color: var(--text-secondary);
        box-shadow: none;
    }

    button.btn-ghost:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    /* 按钮尺寸变种 */
    button.btn-sm {
        padding: var(--space-sm) var(--space-md);
        font-size: var(--font-sm);
        min-height: 36px;
    }

    button.btn-lg {
        padding: var(--space-lg) var(--space-xl);
        font-size: var(--font-lg);
        min-height: 56px;
    }

    /* 按钮组 */
    .btn-group {
        display: flex;
        gap: var(--space-sm);
    }

    .btn-group button {
        flex: 1;
    }

    /* 现代化卡片系统 - 紧凑化 */
    .card {
        background: var(--bg-primary);
        border-radius: var(--radius-lg); /* 减小圆角 */
        padding: var(--space-md); /* 减少内边距 */
        margin-bottom: var(--space-sm); /* 减少底部间距 */
        box-shadow: var(--shadow-sm); /* 减小阴影 */
        border: 1px solid var(--border-light);
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .card:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--space-sm); /* 减少底部间距 */
        padding-bottom: var(--space-sm); /* 减少内边距 */
        border-bottom: 1px solid var(--border-light);
    }

    .card-title {
        font-weight: 600;
        margin: 0;
        font-size: var(--font-base); /* 减小字体 */
        color: var(--text-primary);
        line-height: var(--leading-tight);
    }

    .card-subtitle {
        font-size: var(--font-sm);
        color: var(--text-secondary);
        margin: var(--space-xs) 0 0 0;
    }

    .card-content {
        color: var(--text-secondary);
        line-height: var(--leading-normal);
    }

    .card-footer {
        margin-top: var(--space-lg);
        padding-top: var(--space-md);
        border-top: 1px solid var(--border-light);
    }

    /* 现代化进度条 */
    .progress-bar {
        height: 6px;
        background: var(--bg-tertiary);
        margin: var(--space-md) 0;
        border-radius: var(--radius-full);
        overflow: hidden;
        position: relative;
    }

    .progress {
        height: 100%;
        background: var(--primary-gradient);
        transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: var(--radius-full);
        position: relative;
    }

    .progress::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        animation: shimmer 2s infinite;
    }

    /* 现代化头部系统 */
    .header {
        background: var(--primary-gradient);
        color: var(--text-inverse);
        padding: var(--space-lg) var(--space-md);
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: var(--shadow-lg);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .header h1 {
        margin: 0;
        font-size: var(--font-xl);
        font-weight: 700;
        text-align: center;
        color: var(--text-inverse);
        letter-spacing: -0.025em;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .header-actions {
        position: absolute;
        right: var(--space-md);
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        gap: var(--space-sm);
    }

    .header-action {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-lg);
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: var(--text-inverse);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .header-action:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
    }

    /* 现代化标签页导航 */
    .tab-navigation {
        display: flex;
        background: var(--bg-primary);
        border-bottom: 1px solid var(--border-light);
        position: sticky;
        top: 0; /* 移除头部后调整到顶部 */
        z-index: 90;
        padding: var(--space-xs) var(--space-md);
        backdrop-filter: blur(10px);
        box-shadow: var(--shadow-sm);
        height: 48px; /* 紧凑化高度 */
    }

    .tab-button {
        flex: 1;
        padding: var(--space-sm);
        text-align: center;
        background: none;
        border: none;
        font-size: 13px; /* 紧凑化字体 */
        font-weight: 500;
        color: var(--text-secondary);
        position: relative;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        min-height: 40px; /* 紧凑化高度 */
        border-radius: var(--radius-md);
        transition: all 0.2s ease;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 var(--space-xs);
        flex-direction: column;
    }

    .tab-button:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    .tab-button.active {
        background: var(--primary-gradient);
        color: var(--text-inverse);
        font-weight: 600;
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
    }

    .tab-button.active::before {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 24px;
        height: 3px;
        background: var(--primary-color);
        border-radius: var(--radius-full);
    }
    }

    .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 25%;
        width: 50%;
        height: 3px;
        background: var(--primary-color);
        border-radius: 3px 3px 0 0;
    }

    /* 现代化内容区域 - 紧凑化 */
    .tab-content {
        display: none;
        padding: var(--space-md); /* 减少内边距 */
        background: var(--bg-secondary);
        min-height: calc(100vh - 120px); /* 调整最小高度 */
    }

    .tab-content.active {
        display: block;
        animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* 现代化表单组 - 紧凑化 */
    .form-group {
        margin-bottom: var(--space-md); /* 减少间距 */
        position: relative;
    }

    .form-group.mb-sm {
        margin-bottom: var(--space-sm); /* 减少小间距 */
    }

    label {
        display: block;
        margin-bottom: var(--space-xs); /* 减少底部间距 */
        font-weight: 500; /* 减轻字重 */
        color: var(--text-primary);
        font-size: 13px; /* 紧凑化字体 */
        letter-spacing: -0.025em;
    }

    .label-required::after {
        content: '*';
        color: var(--danger-color);
        margin-left: var(--space-xs);
    }

    .input-helper {
        font-size: var(--font-xs);
        color: var(--text-tertiary);
        margin-top: var(--space-xs);
        line-height: var(--leading-normal);
    }

    .input-error {
        font-size: var(--font-xs);
        color: var(--danger-color);
        margin-top: var(--space-xs);
        display: flex;
        align-items: center;
        gap: var(--space-xs);
    }

    .input-error::before {
        content: '⚠';
        font-size: var(--font-sm);
    }

    /* 输入框状态 */
    .form-group.has-error input,
    .form-group.has-error select,
    .form-group.has-error textarea {
        border-color: var(--danger-color);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .form-group.has-success input,
    .form-group.has-success select,
    .form-group.has-success textarea {
        border-color: var(--success-color);
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }

    /* 触摸优化 */
    button, .menu-item, .tab-button, .service-types label,
    .recharge-amount-option, .recharge-payment-method {
        touch-action: manipulation; /* 优化触摸体验 */
    }

    /* 弹窗样式优化 */
    .modal-content {
        width: 90% !important;
        max-width: 450px;
    }

    /* 表单布局优化 */
    .form-input-row {
        flex-direction: column;
        gap: 10px;
    }

    .form-input-row .form-group {
        width: 100%;
    }

    /* 服务要求区域优化 */
    .service-requirements .form-input-row {
        flex-direction: column;
    }

    .service-requirements .form-group {
        width: 100%;
    }

    /* 现代化衣物项组件 */
    .clothing-item {
        background: var(--bg-primary);
        border-radius: var(--radius-xl);
        padding: var(--space-lg);
        margin-bottom: var(--space-md);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-light);
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        animation: slideUp 0.4s ease-out;
    }

    .clothing-item:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
        border-color: var(--primary-color);
    }

    .clothing-item-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--space-md);
        gap: var(--space-md);
    }

    .clothing-item-title {
        font-weight: 600;
        margin: 0;
        font-size: var(--font-lg);
        color: var(--text-primary);
        flex: 1;
        line-height: var(--leading-tight);
    }

    .clothing-item-number {
        background: var(--primary-gradient);
        color: var(--text-inverse);
        padding: var(--space-xs) var(--space-sm);
        border-radius: var(--radius-full);
        font-size: var(--font-xs);
        font-weight: 600;
        min-width: 24px;
        text-align: center;
    }

    .toggle-collapse {
        background: var(--bg-tertiary);
        color: var(--primary-color);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-lg);
        padding: var(--space-sm) var(--space-md);
        font-size: var(--font-xs);
        font-weight: 500;
        transition: all 0.2s ease;
        min-height: 32px;
        width: auto;
        box-shadow: none;
    }

    .toggle-collapse:hover {
        background: var(--primary-color);
        color: var(--text-inverse);
        border-color: var(--primary-color);
    }

    .remove-item {
        position: absolute;
        right: var(--space-md);
        top: var(--space-md);
        background: var(--danger-color);
        width: 32px;
        height: 32px;
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-inverse);
        font-size: var(--font-sm);
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: var(--shadow-sm);
    }

    .remove-item:hover {
        background: #dc2626;
        transform: scale(1.1);
        box-shadow: var(--shadow-md);
    }

    .clothing-content {
        margin-top: var(--space-md);
        padding-top: var(--space-md);
        border-top: 1px solid var(--border-light);
        animation: fadeIn 0.3s ease;
    }

    .clothing-item.collapsed .clothing-content {
        display: none;
    }

    .clothing-item.collapsed {
        padding-bottom: var(--space-md);
    }

    /* 现代化服务类型选择器 */
    .service-types {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-sm);
        margin-top: var(--space-md);
    }

    .service-types label {
        display: inline-flex;
        align-items: center;
        margin: 0;
        background: var(--bg-tertiary);
        padding: var(--space-md) var(--space-lg);
        border-radius: var(--radius-full);
        font-size: var(--font-sm);
        font-weight: 500;
        border: 2px solid transparent;
        transition: all 0.2s ease;
        cursor: pointer;
        min-height: 44px;
        user-select: none;
    }

    .service-types label:hover {
        background: var(--bg-primary);
        border-color: var(--border-medium);
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    .service-types label.selected {
        background: var(--primary-gradient);
        border-color: var(--primary-color);
        color: var(--text-inverse);
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
    }

    .service-types input[type="checkbox"] {
        display: none; /* 隐藏原生复选框 */
    }

    .service-types label::before {
        content: '';
        width: 18px;
        height: 18px;
        border: 2px solid var(--border-medium);
        border-radius: var(--radius-sm);
        margin-right: var(--space-sm);
        transition: all 0.2s ease;
        background: var(--bg-primary);
    }

    .service-types label.selected::before {
        background: var(--text-inverse);
        border-color: var(--text-inverse);
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236366f1'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M5 13l4 4L19 7'/%3E%3C/svg%3E");
        background-size: 12px;
        background-position: center;
        background-repeat: no-repeat;
    }

    /* 照片区域优化 - 紧凑化 */
    .photo-gallery {
        margin: 6px 0; /* 减少上下间距 */
    }

    .photo-thumbnails {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 6px; /* 减少间距 */
        margin-bottom: 6px; /* 减少底部间距 */
    }

    .photo-thumbnail {
        width: 100%;
        aspect-ratio: 1 / 1;
        border-radius: 6px; /* 减小圆角 */
        object-fit: cover;
        border: 1px solid var(--border-color);
        transition: transform 0.2s;
    }

    .photo-thumbnail:active {
        transform: scale(0.95);
    }

    .photo-thumbnail-container {
        position: relative;
    }

    .remove-photo {
        position: absolute;
        top: -6px;
        right: -6px;
        width: 20px;
        height: 20px;
        background-color: var(--danger-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        z-index: 10;
        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    }

    .add-photo-btn {
        background: var(--background-light);
        color: var(--text-secondary);
        border: 1px dashed var(--border-color);
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .add-photo-btn svg {
        margin-right: 6px;
    }

    /* 服务要求区域优化 */
    .service-requirements {
        background: var(--background-light);
        padding: 12px !important;
        border-radius: 8px;
        margin-top: 10px !important;
        margin: 0;
        border-left: 3px solid var(--primary-color);
        animation: fadeIn 0.3s ease;
    }

    /* 价格输入优化 */
    .price-input-container {
        display: flex;
        align-items: center;
        position: relative;
    }

    .price-input-container:before {
        content: '¥';
        position: absolute;
        left: 10px;
        font-size: 15px;
        color: var(--text-secondary);
    }

    .price-input-container input {
        padding-left: 25px;
    }

    /* 适配横屏布局 */
    @media (orientation: landscape) and (max-height: 500px) {
        .tab-navigation {
            height: 38px;
        }

        .tab-button {
            height: 38px;
            padding: 8px;
        }

        .clothing-item {
            margin-bottom: 10px;
        }

        .form-row {
            display: flex;
            gap: 12px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .photo-thumbnails {
            grid-template-columns: repeat(6, 1fr);
        }
    }

    /* 现代化底部导航 */
    .mobile-menu {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        backdrop-filter: blur(20px);
        border-top: 1px solid var(--border-light);
        padding: var(--space-sm) var(--space-md);
        display: flex;
        justify-content: space-around;
        align-items: center;
        z-index: 1000;
        box-shadow: var(--shadow-xl);
        height: 72px;
    }

    .menu-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: var(--text-tertiary);
        transition: all 0.2s ease;
        padding: var(--space-sm);
        border-radius: var(--radius-lg);
        min-width: 60px;
        position: relative;
    }

    .menu-item:hover {
        color: var(--primary-color);
        background: var(--bg-tertiary);
        transform: translateY(-2px);
    }

    .menu-item.active {
        color: var(--primary-color);
        background: rgba(99, 102, 241, 0.1);
    }

    .menu-item.active::before {
        content: '';
        position: absolute;
        top: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 24px;
        height: 3px;
        background: var(--primary-gradient);
        border-radius: var(--radius-full);
    }

    .menu-icon {
        font-size: var(--font-lg);
        margin-bottom: var(--space-xs);
        transition: transform 0.2s ease;
    }

    .menu-item:hover .menu-icon {
        transform: scale(1.1);
    }

    .menu-item span {
        font-size: var(--font-xs);
        font-weight: 500;
        line-height: 1;
        text-align: center;
    }

    /* 更多菜单弹出样式 */
    .more-menu {
        position: fixed;
        bottom: 55px; /* 调整位置，减小与底部菜单的距离 */
        right: 16px; /* 减小右侧间距 */
        background: white;
        border-radius: 8px; /* 减小圆角 */
        box-shadow: 0 2px 8px rgba(0,0,0,0.15); /* 减小阴影 */
        z-index: 95;
        overflow: hidden;
        display: none;
        animation: slideUp 0.2s ease-out;
    }

    .more-menu.active {
        display: block;
    }

    .more-menu-item {
        padding: 10px 12px; /* 减小内边距 */
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;
        color: #333;
        text-decoration: none;
        font-size: 0.9rem; /* 减小字体大小 */
    }

    .more-menu-item:last-child {
        border-bottom: none;
    }

    .more-menu-icon {
        margin-right: 8px; /* 减小右侧间距 */
        font-size: 1.1rem; /* 减小图标大小 */
    }

    /* 现代化悬浮按钮 */
    .action-button {
        position: fixed;
        width: 56px;
        height: 56px;
        right: var(--space-lg);
        bottom: 96px; /* 调整位置适应新底部菜单 */
        background: var(--primary-gradient);
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-inverse);
        font-size: var(--font-xl);
        font-weight: bold;
        z-index: 999;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: var(--shadow-xl);
        border: none;
        backdrop-filter: blur(10px);
    }

    .action-button:hover {
        transform: translateY(-4px) scale(1.05);
        box-shadow: 0 25px 50px -12px rgba(99, 102, 241, 0.4);
    }

    .action-button:active {
        transform: translateY(-2px) scale(1.02);
    }

    .action-button::before {
        content: '';
        position: absolute;
        inset: -2px;
        background: var(--primary-gradient);
        border-radius: var(--radius-full);
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .action-button:hover::before {
        opacity: 0.7;
        animation: pulse 2s infinite;
    }

    /* 加载指示器样式 */
    .loading-overlay {
        animation: fadeIn 0.2s ease-out;
    }

    .loading-spinner {
        width: 30px; /* 减小尺寸 */
        height: 30px; /* 减小尺寸 */
    }

    .loading-text {
        margin-top: 8px; /* 减小上边距 */
        font-size: 0.9rem; /* 减小字体大小 */
    }

    /* 表单布局优化 */
    .form-input-row {
        flex-direction: column;
        gap: 6px; /* 减小间距 */
    }

    /* 优化表单元素间距 */
    .form-group {
        margin-bottom: 6px; /* 进一步减少表单组底部间距 */
    }

    .form-group + .form-group {
        margin-top: 8px; /* 减少相邻表单组间距至8px */
    }

    /* 标签样式优化 */
    label {
        font-size: 0.8rem; /* 进一步减小标签字体 */
        margin-bottom: 1px; /* 减少标签下边距至1px */
        display: block;
        color: #555; /* 使标签颜色稍微淡一些，减少视觉干扰 */
        line-height: 1.1; /* 减小行高 */
    }

    /* 优化选择器样式 */
    select {
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 8px center; /* 减小右侧内边距 */
        background-size: 14px; /* 减小图标大小 */
        padding-right: 25px; /* 减小右侧内边距 */
    }

    /* 按钮样式优化 */
    button {
        padding: 4px 8px; /* 进一步减小内边距 */
        font-size: 0.8rem; /* 进一步减小字体 */
        min-height: 32px; /* 减小最小高度至32px */
        border-radius: 4px; /* 保持小圆角 */
        margin: 0; /* 移除默认边距 */
        border: 1px solid transparent; /* 细边框 */
        line-height: 1.2; /* 减小行高 */
    }

    /* 优化复选框样式 */
    input[type="checkbox"] {
        width: 16px; /* 减小尺寸 */
        height: 16px; /* 减小尺寸 */
        margin-right: 6px; /* 减小右侧间距 */
        -webkit-appearance: none;
        appearance: none;
        background-color: #fff;
        border: 1px solid #ccc; /* 减小边框粗细 */
        border-radius: 3px; /* 减小圆角 */
        display: inline-block;
        position: relative;
        vertical-align: middle;
        cursor: pointer;
        transition: background 0.2s, border-color 0.2s; /* 加快过渡动画 */
    }

    input[type="checkbox"]:checked {
        background-color: #007BFF;
        border-color: #007BFF;
    }

    input[type="checkbox"]:checked::after {
        content: '';
        position: absolute;
        left: 5px; /* 调整勾选标记位置 */
        top: 2px;
        width: 4px; /* 减小勾选标记尺寸 */
        height: 8px; /* 减小勾选标记尺寸 */
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    input[type="checkbox"]:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25); /* 减小焦点阴影 */
    }

    .service-types label {
        display: flex;
        align-items: center;
        padding: 4px 8px; /* 进一步减小内边距 */
        background-color: #f5f5f5;
        border-radius: 4px; /* 减小圆角 */
        margin-right: 4px; /* 减小右侧间距 */
        margin-bottom: 4px; /* 减小底部间距 */
        cursor: pointer;
        transition: background-color 0.2s;
        font-size: 0.8rem; /* 减小字体大小 */
        height: 28px; /* 固定高度 */
    }

    .service-types label:hover {
        background-color: #e9e9e9;
    }

    /* 优化标签页内容 */
    .tab-content {
        animation: fadeIn 0.2s ease-out;
        padding: 8px; /* 减小内边距 */
    }

    /* 优化弹窗样式 */
    .modal-content {
        padding: 12px; /* 减小内边距 */
    }

    /* 优化弹窗动画 */
    #cameraModal, #rechargeModal, #receiptModal, #labelModal {
        animation: fadeIn 0.2s ease-out;
    }

    /* 优化弹窗标题和内容间距 */
    .modal-header {
        margin-bottom: 8px; /* 减小标题下边距 */
    }

    .modal-body {
        padding: 8px 0; /* 减小内边距 */
    }

    /* 优化服务类型选择器 */
    .service-types {
        flex-wrap: wrap;
        gap: 5px; /* 减小间距 */
        margin-bottom: 6px; /* 减小底部间距 */
    }

    .service-types label {
        flex: 1;
        min-width: 80px; /* 减小最小宽度 */
        justify-content: center;
        margin: 0; /* 移除默认边距，使用gap控制 */
    }

    /* 服务要求区域优化 */
    .service-requirements {
        padding: 8px !important; /* 减小内边距 */
        margin-top: 6px !important; /* 减小上边距 */
        margin-bottom: 6px !important; /* 减小下边距 */
    }

    /* 优化照片缩略图区域 */
    .photo-gallery {
        margin-bottom: 4px; /* 减小底部间距 */
    }

    .photo-thumbnails {
        justify-content: flex-start;
        gap: 4px; /* 减小间距 */
        margin-bottom: 4px; /* 减小底部间距 */
        display: flex;
        flex-wrap: wrap;
    }

    .photo-thumbnail {
        width: 45px; /* 减小缩略图尺寸 */
        height: 45px; /* 减小缩略图尺寸 */
        margin: 0; /* 移除默认边距，使用gap控制 */
        object-fit: cover; /* 确保图片填充整个区域 */
        border-radius: 3px; /* 添加小圆角 */
    }

    .photo-thumbnail-container {
        position: relative;
        width: 45px; /* 与缩略图尺寸一致 */
        height: 45px; /* 与缩略图尺寸一致 */
    }

    .remove-photo {
        position: absolute;
        top: -5px;
        right: -5px;
        width: 16px; /* 减小尺寸 */
        height: 16px; /* 减小尺寸 */
        font-size: 12px; /* 减小字体 */
        line-height: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ff4d4d;
        color: white;
        border-radius: 50%;
        cursor: pointer;
    }

    .add-photo-btn {
        padding: 4px; /* 减小内边距 */
        font-size: 0.75rem; /* 减小字体大小 */
        height: 28px; /* 固定高度 */
        line-height: 1.1; /* 减小行高 */
    }

    /* 优化充值选项 */
    .recharge-amount-option, .recharge-payment-method {
        transition: all 0.2s ease;
        padding: 6px; /* 减小内边距至6px */
        margin-bottom: 4px; /* 减小底部间距 */
        font-size: 0.8rem; /* 减小字体大小 */
        border-radius: 4px; /* 减小圆角 */
        height: 32px; /* 固定高度 */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .recharge-amount-option.selected, .recharge-payment-method.selected {
        background-color: #007BFF;
        color: white;
        border-color: #007BFF;
    }

    /* 优化充值选项容器 */
    .recharge-amount-options, .recharge-payment-methods {
        gap: 4px; /* 减小间距 */
        margin-bottom: 6px; /* 减小底部间距 */
        display: flex;
        flex-wrap: wrap;
    }

    /* 优化弹窗样式 */
    .modal-content {
        padding: 8px; /* 减小内边距至8px */
    }

    .modal-header {
        margin-bottom: 6px; /* 减小底部间距 */
        padding: 0; /* 移除内边距 */
    }

    .modal-header h2 {
        font-size: 1rem; /* 减小标题字体 */
        margin: 0; /* 移除边距 */
    }

    .modal-body {
        padding: 6px 0; /* 减小内边距 */
    }

    .modal-footer {
        margin-top: 6px; /* 减小上边距 */
        padding: 0; /* 移除内边距 */
    }

    /* 优化摘要项样式 */
    .summary-item {
        background: white;
        border-radius: 4px; /* 减小圆角 */
        padding: 6px; /* 减小内边距至6px */
        margin-bottom: 5px; /* 减小底部间距 */
        box-shadow: 0 1px 2px rgba(0,0,0,0.05); /* 保持小阴影 */
    }

    /* 优化摘要项内部间距 */
    .summary-item h3 {
        margin-bottom: 3px; /* 减小标题下边距 */
        font-size: 0.95rem; /* 减小标题字体 */
    }

    .summary-item p {
        margin: 1px 0; /* 减小段落上下边距至1px */
        font-size: 0.85rem; /* 减小段落字体 */
    }

    /* 优化打印预览区域 */
    #receiptContent, #labelContent {
        background: #f9f9f9;
        padding: 6px; /* 减小内边距至6px */
        border-radius: 4px; /* 减小圆角 */
        margin-bottom: 6px; /* 减小底部间距 */
        font-size: 0.85rem; /* 减小字体大小 */
    }

    /* 优化标签页内容区域 */
    .tab-content {
        animation: fadeIn 0.2s ease-out;
        padding: 6px; /* 减小内边距至6px */
    }
}

/* 针对特小屏幕的优化 - 极致紧凑化 */
@media (max-width: 360px) {
    body {
        font-size: 11px; /* 进一步减小基础字体 */
        padding-bottom: 30px; /* 减小底部菜单空间 */
        line-height: 1.2; /* 减小行高 */
    }

    /* 进一步优化标题 */
    h1, h2, h3, h4, h5, h6 {
        margin: 0.15rem 0; /* 进一步减少边距 */
        line-height: 1.1; /* 减小行高 */
    }

    h1 { font-size: 1rem; } /* 进一步减小 */
    h2 { font-size: 0.9rem; }
    h3 { font-size: 0.8rem; }

    /* 进一步优化标签页导航 */
    .tab-navigation {
        height: 28px; /* 进一步减小高度 */
        top: 36px; /* 调整位置 */
    }

    .tab-button {
        font-size: 0.65rem; /* 进一步减小字体 */
        padding: 3px 2px; /* 减小内边距 */
        height: 28px; /* 固定高度 */
    }

    .header {
        padding: 6px 10px; /* 减小内边距 */
    }

    .header h1 {
        font-size: 1rem; /* 进一步减小标题字体 */
    }

    /* 进一步优化服务类型标签 */
    .service-types {
        gap: 4px; /* 减小间距 */
        margin-top: 4px;
    }

    .service-types label {
        padding: 4px 6px; /* 调整内边距 */
        font-size: 0.7rem; /* 减小字体 */
        min-height: 22px; /* 减小最小高度 */
        border-radius: 12px; /* 减小圆角 */
    }

    /* 进一步优化表单元素 */
    input, select, textarea {
        font-size: 11px; /* 进一步减小字体 */
        padding: 5px; /* 调整内边距 */
        height: 28px; /* 减小高度 */
        margin-bottom: 2px; /* 减小底部间距 */
    }

    textarea {
        min-height: 40px; /* 减小文本区域高度 */
        padding: 4px;
    }

    /* 进一步优化按钮 */
    button {
        font-size: 0.7rem; /* 进一步减小字体 */
        padding: 6px 8px; /* 调整内边距 */
        height: 30px; /* 减小高度 */
    }

    button.btn-sm {
        font-size: 0.65rem;
        padding: 4px 6px;
        height: 26px;
    }

    /* 优化底部菜单 */
    .mobile-menu {
        height: 30px; /* 进一步减小高度 */
    }

    .menu-icon {
        font-size: 0.8rem; /* 进一步减小图标 */
    }

    .menu-item span {
        font-size: 0.6rem; /* 进一步减小文字 */
    }

    /* 优化衣物项 */
    .clothing-item {
        padding: 8px; /* 减小内边距 */
        margin-bottom: 8px; /* 减小间距 */
    }

    .clothing-item-header {
        min-height: 20px; /* 减小最小高度 */
        margin-bottom: 4px;
    }

    .clothing-item-title {
        font-size: 0.8rem; /* 进一步减小字体 */
    }

    .toggle-collapse {
        font-size: 0.65rem; /* 减小字体 */
        padding: 2px 6px; /* 减小内边距 */
        min-height: 20px;
    }

    .remove-item {
        width: 20px; /* 进一步减小 */
        height: 20px;
        font-size: 12px;
        right: 6px;
        top: 6px;
    }

    /* 优化照片缩略图 */
    .photo-thumbnails {
        gap: 4px; /* 减小间距 */
        grid-template-columns: repeat(5, 1fr); /* 增加列数 */
    }

    .photo-thumbnail, .photo-thumbnail-container {
        border-radius: 4px; /* 减小圆角 */
    }

    /* 优化标签页内容 */
    .tab-content {
        padding: 6px; /* 调整内边距 */
    }

    /* 优化表单组 */
    .form-group {
        margin-bottom: 6px; /* 减小间距 */
    }

    .form-group.mb-sm {
        margin-bottom: 3px;
    }

    label {
        font-size: 0.7rem; /* 减小标签字体 */
        margin-bottom: 2px;
    }

    /* 优化表单行 */
    .form-row {
        gap: 4px; /* 减小间距 */
        margin-bottom: 4px;
    }

    /* 优化卡片 */
    .card {
        padding: 8px; /* 减小内边距 */
        margin-bottom: 6px;
    }

    .card-header {
        margin-bottom: 4px;
        padding-bottom: 3px;
    }

    .card-title {
        font-size: 0.8rem; /* 减小字体 */
    }

    /* 优化进度条 */
    .progress-bar {
        height: 2px; /* 进一步减小 */
        margin: 4px 0;
    }

    /* 优化悬浮按钮 - 小屏设备 */
    .action-button {
        width: 36px; /* 进一步减小 */
        height: 36px; /* 进一步减小 */
        right: 10px; /* 减小右侧间距 */
        bottom: 35px; /* 调整位置 */
        font-size: 1rem; /* 减小图标 */
    }
}

/* 针对大屏手机的优化 */
@media (min-width: 400px) and (max-width: 768px) {
    .photo-thumbnail {
        width: 80px;
        height: 80px;
    }

    .service-types label {
        min-width: 120px;
    }
}

/* 针对平板设备的优化 */
@media (min-width: 768px) and (max-width: 1024px) {
    body {
        max-width: 768px;
        margin: 0 auto;
    }

    .mobile-menu {
        max-width: 768px;
        left: 50%;
        transform: translateX(-50%);
    }

    .action-button {
        right: calc(50% - 384px + 20px);
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #121212;
        color: #e0e0e0;
    }

    .header {
        background-color: #1a1a1a;
    }

    .tab-navigation {
        background-color: #1a1a1a;
        border-bottom-color: #333;
    }

    .tab-button {
        color: #aaa;
    }

    .tab-button.active {
        color: #4da3ff;
    }

    .tab-button.active::after {
        background: #4da3ff;
    }

    input, select, textarea {
        background-color: #2a2a2a;
        border-color: #444;
        color: #e0e0e0;
    }

    .clothing-item, .summary-item {
        background-color: #1a1a1a;
        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    }

    .service-types label {
        background-color: #2a2a2a;
    }

    .mobile-menu {
        background-color: #1a1a1a;
        border-top-color: #333;
    }

    .more-menu {
        background-color: #1a1a1a;
    }

    .more-menu-item {
        border-bottom-color: #333;
    }
}
