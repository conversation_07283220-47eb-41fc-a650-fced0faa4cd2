# 状态处理页面订单详情跳转功能说明

## 功能概述

在状态处理页面中新增了"查看/编辑订单详情"功能，允许操作员在更新订单状态后，快速跳转到历史订单详情页面查看和编辑订单信息。

## 功能特点

### 1. 智能链接按钮
- 在状态更新成功后的结果显示区域，自动显示"查看/编辑订单详情"按钮
- 按钮具有现代化的UI设计，包含图标和悬停效果
- 按钮仅在状态更新成功后显示，确保有有效的订单信息

### 2. 自动参数传递
- 点击按钮后自动传递当前订单号参数到历史页面
- 历史页面自动填充订单号并执行搜索
- 无需手动输入订单号，提高操作效率

### 3. 新标签页打开
- 在新标签页中打开历史订单详情页面
- 保持状态处理页面继续可用，方便连续操作
- 避免页面跳转导致的工作流中断

### 4. 复用现有功能
- 完全复用现有的历史订单详情页面和编辑功能
- 不创建新的页面或组件，保持系统一致性
- 利用现有的订单编辑、打印等完整功能

## 适用页面

此功能已在所有状态处理页面中实现：

- **送洗处理页面** (`/status/to_factory`) - 更新状态为"送至工厂中"
- **入厂处理页面** (`/status/factory_in`) - 更新状态为"入厂"
- **出厂处理页面** (`/status/factory_out`) - 更新状态为"出厂"
- **上架处理页面** (`/status/on_shelf`) - 更新状态为"已上架"
- **自取处理页面** (`/status/self_pickup`) - 更新状态为"已自取"

## 使用流程

### 1. 状态更新操作
1. 在任意状态处理页面输入订单号或扫描水洗唛条码
2. 点击"确认更新"按钮
3. 系统显示状态更新成功信息

### 2. 查看订单详情
1. 在更新成功的结果区域，点击"查看/编辑订单详情"按钮
2. 系统在新标签页中打开历史订单页面
3. 自动搜索并显示对应订单的详细信息

### 3. 编辑订单信息
1. 在历史订单详情页面中，可以查看完整的订单信息
2. 点击"详情"按钮打开订单详情模态框
3. 使用"编辑"按钮修改衣物信息、价格等
4. 使用打印功能生成小票或水洗唛

## 技术实现

### 前端实现
- 在状态更新模板中添加按钮HTML结构
- 使用CSS美化按钮样式和交互效果
- JavaScript处理按钮点击事件和URL构建
- 存储当前订单号用于参数传递

### 后端支持
- 历史页面支持URL参数自动搜索
- 复用现有的订单查询和详情API
- 保持现有的权限控制和数据安全

### URL参数格式
```
/history?order_number=订单号
```

例如：`/history?order_number=100004`

## 优势

1. **提高操作效率** - 无需重新搜索订单，一键跳转到详情页面
2. **保持工作连续性** - 新标签页打开，不影响当前状态处理工作
3. **功能完整性** - 直接访问完整的订单编辑和管理功能
4. **用户体验优化** - 简化操作流程，减少重复输入
5. **系统一致性** - 复用现有组件，保持界面和功能的一致性

## 注意事项

1. 按钮仅在状态更新成功后显示
2. 需要有效的订单号才能正确跳转
3. 新标签页的打开可能受浏览器弹窗拦截设置影响
4. 历史页面的权限控制仍然有效，确保数据安全

## 维护说明

- 按钮样式定义在状态更新模板的CSS部分
- 跳转逻辑在JavaScript的showResult函数中
- 历史页面的URL参数处理在DOMContentLoaded事件中
- 如需修改按钮文本或样式，编辑对应的HTML和CSS代码
