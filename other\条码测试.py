#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 引入所需库
import barcode
from barcode.writer import ImageWriter
import os

try:
    # 定义要编码的数字
    number = '10000000010'

    # 选择条形码格式（这里用Code128）
    code_type = barcode.get_barcode_class('code128')

    # 生成条形码对象
    generated_code = code_type(number, writer=ImageWriter())

    # 保存为图片
    filename = generated_code.save('barcode')

    print(f'条形码已保存为 {filename}')
    print(f'保存路径: {os.path.abspath(filename)}')
    
except Exception as e:
    print(f"发生错误: {e}")