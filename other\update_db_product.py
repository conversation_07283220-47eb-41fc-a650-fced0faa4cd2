from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import pymysql
import sys
from sqlalchemy import text

# 将PyMySQL注册为MySQLdb
pymysql.install_as_MySQLdb()

app = Flask(__name__)

# 数据库配置
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://ytgf:ZEzaJikm2kNakFbk@49.232.0.106:3306/ytgf'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

def update_db():
    """创建商品表并导入初始商品数据"""
    with app.app_context():
        try:
            # 创建商品表
            print("创建商品表...")
            # 先删除表（如果存在）
            db.session.execute(text("DROP TABLE IF EXISTS product"))
            
            # 然后创建新表
            db.session.execute(text("""
                CREATE TABLE product (
                    id INTEGER PRIMARY KEY AUTO_INCREMENT,
                    name VARCHAR(100) NOT NULL UNIQUE,
                    category VARCHAR(50) NOT NULL,
                    price FLOAT NOT NULL DEFAULT 0.0,
                    description VARCHAR(255),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """))
            
            # 导入初始商品数据
            print("导入初始商品数据...")
            initial_products = [
                ("衬衫", "上衣", 15, "常规衬衫"),
                ("西装上衣", "上衣", 25, "正装西装上衣"),
                ("西装裤", "裤装", 20, "正装西装裤"),
                ("羽绒服", "外套", 30, "保暖羽绒服"),
                ("牛仔裤", "裤装", 18, "常规牛仔裤"),
                ("连衣裙", "裙装", 28, "常规连衣裙"),
                ("羊毛衫", "上衣", 22, "保暖羊毛衫"),
                ("T恤", "上衣", 12, "常规T恤"),
                ("风衣", "外套", 35, "常规风衣"),
                ("大衣", "外套", 40, "保暖大衣"),
                ("睡衣", "套装", 15, "睡衣套装"),
                ("羊绒衫", "上衣", 30, "高档羊绒衫"),
                ("皮衣", "外套", 50, "皮质外套"),
                ("运动服", "套装", 25, "运动套装"),
                ("内衣", "其他", 10, "基础内衣")
            ]
            
            # 检查并导入初始商品
            for product in initial_products:
                name, category, price, description = product
                existing = db.session.execute(text(
                    "SELECT id FROM product WHERE name = :name"
                ), {"name": name}).fetchone()
                
                if not existing:
                    db.session.execute(text("""
                        INSERT INTO product (name, category, price, description, is_active)
                        VALUES (:name, :category, :price, :description, TRUE)
                    """), {
                        "name": name,
                        "category": category,
                        "price": price,
                        "description": description
                    })
                    print(f"添加商品: {name}")
            
            db.session.commit()
            print("商品表创建和初始数据导入成功！")
            return True
        
        except Exception as e:
            db.session.rollback()
            print(f"数据库更新失败: {str(e)}")
            return False

if __name__ == "__main__":
    print("开始更新数据库...")
    success = update_db()
    
    if success:
        print("数据库更新成功!")
        sys.exit(0)
    else:
        print("数据库更新失败，请检查错误信息。")
        sys.exit(1)
