<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soulweave改衣坊 - 营业员登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 350px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            border-color: #007BFF;
            outline: none;
        }
        .login-button {
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px;
            width: 100%;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .login-button:hover {
            background-color: #0056b3;
        }
        .error-message {
            color: #dc3545;
            margin-top: 15px;
            text-align: center;
            display: none;
        }
        .debug-tools {
            margin-top: 20px;
            text-align: center;
        }
        .debug-button {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .debug-button:hover {
            background-color: #5a6268;
        }
        .debug-info {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>Soulweave改衣坊</h1>
            <p>营业员登录</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="login-button">登录</button>
            
            <div class="error-message" id="errorMessage"></div>
            
            <div class="debug-tools">
                <button type="button" id="resetAdmin" class="debug-button">重置管理员账号</button>
                <div id="debugInfo" class="debug-info"></div>
            </div>
        </form>
    </div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showError('请输入用户名和密码');
                return;
            }
            
            try {
                showDebugInfo(`正在尝试登录... (${username}/${password})`);
                
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                showDebugInfo(`收到响应: 状态码 ${response.status}`);
                
                if (response.ok) {
                    // 登录成功，重定向到主页
                    showDebugInfo('登录成功，即将跳转...');
                    window.location.href = '/';
                } else {
                    const data = await response.json();
                    showError(data.error || '登录失败，请检查用户名和密码');
                    showDebugInfo(`登录失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                showError('网络错误，请稍后重试');
                showDebugInfo(`登录过程中出错: ${error.message}`);
                console.error('登录错误:', error);
            }
        });
        
        // 添加重置管理员账号的功能
        document.getElementById('resetAdmin').addEventListener('click', async function() {
            try {
                showDebugInfo('正在重置管理员账号...');
                
                const response = await fetch('/reset_admin');
                const data = await response.json();
                
                if (response.ok) {
                    showDebugInfo(`管理员账号重置成功: ${data.username}/${data.password}`);
                    document.getElementById('username').value = data.username;
                    document.getElementById('password').value = data.password;
                } else {
                    showDebugInfo(`重置失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                showDebugInfo(`重置过程中出错: ${error.message}`);
            }
        });
        
        function showError(message) {
            const errorElement = document.getElementById('errorMessage');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
        
        function showDebugInfo(message) {
            const debugElement = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            const newMessage = document.createElement('div');
            newMessage.textContent = `[${timestamp}] ${message}`;
            debugElement.appendChild(newMessage);
            
            // 保持滚动到最新消息
            debugElement.scrollTop = debugElement.scrollHeight;
        }
    </script>
</body>
</html> 