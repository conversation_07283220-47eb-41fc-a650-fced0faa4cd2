# 价格一致性修复 - 修改说明

## 修改概述

本次修改解决了创建新订单时显示的商品价格与商品管理页面中设置的商品价格不一致的问题。通过添加新的API接口和修改前端逻辑，确保选择商品时使用该商品在数据库中的实际服务价格。

## 🔍 问题分析

### 原始问题
1. **价格数据源不统一**：
   - 商品管理页面显示的是具体商品的服务价格（wash_price、mend_price、alter_price、other_price）
   - 创建订单时使用的是第一个商品的服务价格作为默认价格
   - 导致选择不同商品时价格不会更新

2. **getServicePrice()函数局限性**：
   - 只返回第一个商品的价格，不考虑用户选择的具体商品
   - 无法反映不同商品之间的价格差异

3. **商品选择逻辑缺陷**：
   - 选择商品时只使用默认价格，没有获取该商品的实际价格
   - 价格更新不及时，无法反映商品管理页面的价格修改

## 🔧 修复方案

### 1. 新增API接口

**文件**: `app.py` (第3223-3261行)

```python
@app.route('/api/product_prices/<product_name>', methods=['GET'])
@login_required
def get_product_prices(product_name):
    """根据商品名称获取具体商品的服务价格"""
    try:
        # 查找指定名称的商品
        product = Product.query.filter_by(name=product_name, is_active=True).first()

        if product:
            product_prices = {
                '洗衣': product.wash_price,
                '织补': product.mend_price,
                '改衣': product.alter_price,
                '其他': product.other_price
            }
            return jsonify({
                'success': True,
                'product_name': product_name,
                'service_prices': product_prices
            })
        else:
            # 如果找不到商品，返回默认价格
            default_prices = {
                '洗衣': 15.0,
                '织补': 20.0,
                '改衣': 30.0,
                '其他': 20.0
            }
            return jsonify({
                'success': True,
                'product_name': product_name,
                'service_prices': default_prices,
                'is_default': True
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
```

**功能说明**：
- 根据商品名称查询数据库中的具体商品
- 返回该商品的四种服务类型价格
- 如果商品不存在，返回默认价格并标记为默认值

### 2. 前端价格获取函数

**文件**: `static/js/clothing-options.js` (第113-134行)

```javascript
/**
 * 根据商品名称获取具体商品的服务价格
 * @param {string} productName 商品名称
 * @returns {Promise<Object>} 返回包含服务价格的Promise
 */
async function getProductServicePrices(productName) {
    try {
        const response = await fetch(`/api/product_prices/${encodeURIComponent(productName)}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.service_prices) {
                console.log(`获取商品 "${productName}" 的服务价格:`, data.service_prices);
                return data.service_prices;
            }
        }
    } catch (error) {
        console.warn(`无法获取商品 "${productName}" 的服务价格，使用默认价格:`, error);
    }
    
    // 如果获取失败，返回默认价格
    return databaseServicePrices || defaultServicePrices;
}
```

**功能说明**：
- 异步获取指定商品的服务价格
- 包含错误处理和降级机制
- 返回标准化的价格对象

### 3. 首页商品选择逻辑修改

**文件**: `templates/index.html` (第1285-1356行)

#### 修改前的问题
```javascript
// 基础价格概念已移除，选择商品时使用默认服务价格
if (this.value) {
    console.log(`选择商品: ${this.value}，使用默认服务价格`);

    // 使用数据库中的默认服务价格
    const washPriceInput = this.closest('.clothing-item').querySelector('.wash-price');
    if (washPriceInput && typeof getServicePrice === 'function') {
        washPriceInput.value = getServicePrice('洗衣').toFixed(2);
        if (clothingItemsData[itemIndex]) {
            clothingItemsData[itemIndex].specialRequirements.washPrice = getServicePrice('洗衣');
        }
    }
}
```

#### 修改后的改进
```javascript
// 根据选择的商品获取具体的服务价格
if (this.value) {
    console.log(`选择商品: ${this.value}，获取该商品的服务价格`);

    try {
        // 获取该商品的具体服务价格
        const productPrices = await getProductServicePrices(this.value);
        
        // 更新各服务价格输入框
        const clothingItem = this.closest('.clothing-item');
        const washPriceInput = clothingItem.querySelector('.wash-price');
        const darnPriceInput = clothingItem.querySelector('.darn-price');
        const alterPriceInput = clothingItem.querySelector('.alter-price');
        const otherPriceInput = clothingItem.querySelector('.other-price');

        if (washPriceInput) {
            washPriceInput.value = productPrices['洗衣'].toFixed(2);
            if (clothingItemsData[itemIndex]) {
                clothingItemsData[itemIndex].specialRequirements.washPrice = productPrices['洗衣'];
            }
        }
        // ... 其他服务价格更新逻辑

        console.log(`商品 "${this.value}" 的服务价格已更新:`, productPrices);
    } catch (error) {
        console.error('获取商品服务价格失败:', error);
        // 降级处理：使用默认价格
    }
}
```

**改进说明**：
- **异步获取**：使用async/await获取具体商品的服务价格
- **全面更新**：同时更新所有四种服务类型的价格输入框
- **数据同步**：更新clothingItemsData中的价格数据
- **错误处理**：包含完整的错误处理和降级机制

### 4. 移动端商品选择逻辑修改

**文件**: `static/js/mobile-functions.js` (第1525-1579行)

移动端的修改与桌面端类似，确保移动端用户也能获得一致的价格体验：

```javascript
// 根据选择的商品获取具体的服务价格
if (selectedOption.value) {
    console.log(`移动端选择商品: ${selectedOption.value}，获取该商品的服务价格`);

    try {
        // 获取该商品的具体服务价格
        const productPrices = await getProductServicePrices(selectedOption.value);
        
        // 更新各服务价格输入框
        const washPriceInput = newItem.querySelector('.wash-price');
        const darnPriceInput = newItem.querySelector('.darn-price');
        const alterPriceInput = newItem.querySelector('.alter-price');
        const otherPriceInput = newItem.querySelector('.other-price');

        if (washPriceInput) {
            washPriceInput.value = productPrices['洗衣'].toFixed(2);
        }
        // ... 其他价格更新

        console.log(`移动端商品 "${selectedOption.value}" 的服务价格已更新:`, productPrices);
    } catch (error) {
        console.error('移动端获取商品服务价格失败:', error);
        // 降级处理
    }
}
```

## ✅ 修复效果验证

### 价格一致性检查表

| 检查项目 | 修复前 | 修复后 | 状态 |
|----------|--------|--------|------|
| 商品管理页面价格 | ✅ 显示具体商品价格 | ✅ 显示具体商品价格 | ✅ 一致 |
| 创建订单时价格 | ❌ 使用第一个商品价格 | ✅ 使用选中商品价格 | ✅ 修复 |
| 价格实时更新 | ❌ 不会更新 | ✅ 实时获取 | ✅ 修复 |
| 移动端价格 | ❌ 使用默认价格 | ✅ 使用选中商品价格 | ✅ 修复 |
| 错误处理 | ❌ 无降级机制 | ✅ 完整错误处理 | ✅ 改进 |

### 数据流验证

1. **商品管理页面修改价格** → 数据库更新
2. **创建订单选择商品** → 调用`/api/product_prices/<product_name>`
3. **API查询数据库** → 返回最新价格
4. **前端更新价格输入框** → 显示正确价格
5. **用户确认提交** → 使用正确价格创建订单

## 🧪 测试建议

### 功能测试
1. **商品价格修改测试**：
   - 在商品管理页面修改某个商品的服务价格
   - 立即在创建订单页面选择该商品
   - 验证显示的价格是否为修改后的价格

2. **不同商品价格测试**：
   - 创建多个商品，设置不同的服务价格
   - 在创建订单时依次选择不同商品
   - 验证价格是否正确切换

3. **错误处理测试**：
   - 选择不存在的商品名称
   - 网络异常情况
   - 验证是否正确降级到默认价格

### 性能测试
1. **API响应时间**：验证`/api/product_prices/<product_name>`接口响应速度
2. **前端更新速度**：验证价格输入框更新的流畅性
3. **缓存机制**：考虑为频繁查询的商品价格添加缓存

## 📝 注意事项

1. **向后兼容性**：保持原有的`getServicePrice()`函数，确保其他代码正常工作
2. **错误处理**：所有价格获取操作都包含完整的错误处理机制
3. **用户体验**：价格更新过程对用户透明，不影响操作流程
4. **数据一致性**：确保商品管理页面的价格修改能够立即反映到订单创建过程中

## ✅ 修复完成状态

- [x] 新增商品价格获取API接口
- [x] 添加前端价格获取函数
- [x] 修改首页商品选择逻辑
- [x] 修改移动端商品选择逻辑
- [x] 完善错误处理机制
- [x] 保持向后兼容性
- [x] 文档编写

价格一致性问题已完全修复，现在创建订单时显示的服务价格与商品管理页面中设置的价格完全一致，确保了系统数据的统一性和用户体验的一致性。
