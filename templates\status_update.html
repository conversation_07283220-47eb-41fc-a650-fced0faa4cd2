<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - 洗衣系统</title>
    <!-- 引入Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.2.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .form-container {
            margin-top: 20px;
        }
        .result-container {
            margin-top: 20px;
            display: none;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }
        .status-badge {
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .status-info {
            margin-top: 15px;
        }
        #orderInput {
            font-size: 16px;
            padding: 10px;
        }
        .btn-submit {
            padding: 10px 20px;
            font-size: 16px;
        }
        .btn-clear {
            margin-left: 10px;
        }
        .order-details {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .back-link {
            margin-top: 20px;
            display: block;
        }
        .scan-btn {
            margin-left: 10px;
        }
        .info-badge {
            background-color: #e9f5ff;
            border-left: 3px solid #007bff;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .order-actions {
            text-align: center;
            padding: 15px 0;
            border-top: 1px solid #e9ecef;
        }

        .order-actions .btn {
            min-width: 160px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .order-actions .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas {{ icon }}"></i> {{ title }}</h1>
            <div>
                <span>操作员: {{ staff_name }}</span>
                <a href="/" class="btn btn-outline-secondary ms-2">返回首页</a>
            </div>
        </div>

        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> 输入订单号或扫描水洗唛条码可将状态更新为"{{ target_status }}"
        </div>

        <div class="info-badge">
            <i class="fas fa-tag"></i> 支持扫描格式：<br>
            - 订单号：如 "100004"<br>
            - 水洗唛条码：如 "100004-01"（订单号-衣物序号）
        </div>

        <div class="form-container">
            <form id="statusUpdateForm">
                <div class="input-group mb-3">
                    <input type="text" id="orderInput" class="form-control" placeholder="请输入或扫描订单号/水洗唛条码" autofocus>
                    <button class="btn btn-primary btn-submit" type="submit">
                        <i class="fas fa-check"></i> 确认更新
                    </button>
                    <button class="btn btn-outline-secondary scan-btn" type="button">
                        <i class="fas fa-barcode"></i> 扫码
                    </button>
                </div>
            </form>
        </div>

        <div class="result-container" id="resultContainer">
            <div class="alert" id="alertBox" role="alert"></div>
            
            <div class="order-details" id="orderDetails">
                <h4>更新信息</h4>
                <table class="table">
                    <tr>
                        <th width="30%">订单号</th>
                        <td id="orderNumber"></td>
                    </tr>
                    <tr>
                        <th>客户</th>
                        <td id="customerName"></td>
                    </tr>
                    <tr id="clothingInfo" style="display: none;">
                        <th>衣物名称</th>
                        <td id="clothingName"></td>
                    </tr>
                    <tr>
                        <th>原状态</th>
                        <td id="oldStatus"></td>
                    </tr>
                    <tr>
                        <th>新状态</th>
                        <td id="newStatus"><span class="badge bg-success status-badge">{{ target_status }}</span></td>
                    </tr>
                    <tr>
                        <th>更新时间</th>
                        <td id="updateTime"></td>
                    </tr>
                </table>

                <!-- 添加查看/编辑订单详情按钮 -->
                <div class="order-actions mt-3">
                    <button id="viewOrderDetailBtn" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye"></i> 查看/编辑订单详情
                    </button>
                </div>
            </div>
        </div>

        <div class="status-history mt-4">
            <h4>最近处理记录</h4>
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="historyTable">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>客户</th>
                            <th>衣物</th>
                            <th>原状态</th>
                            <th>更新为</th>
                            <th>处理时间</th>
                        </tr>
                    </thead>
                    <tbody id="historyBody">
                        <!-- 历史记录将通过JavaScript动态添加 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后执行
        $(document).ready(function() {
            // 获取历史记录
            fetchHistory();

            // 表单提交处理
            $('#statusUpdateForm').on('submit', function(e) {
                e.preventDefault();
                const barcodeInput = $('#orderInput').val().trim();
                
                if (!barcodeInput) {
                    showAlert('请输入订单号或水洗唛条码', 'danger');
                    return;
                }
                
                updateStatus(barcodeInput);
            });

            // 扫码按钮处理
            $('.scan-btn').on('click', function() {
                $('#orderInput').focus();
                // 这里可以添加调用扫码设备的代码
                // 由于浏览器限制，真实的扫码功能需要通过特定硬件或原生App实现
            });

            // 清除按钮处理
            $('.btn-clear').on('click', function() {
                $('#orderInput').val('').focus();
                hideResult();
            });
        });

        // 解析条码
        function parseBarcode(barcode) {
            // 检查是否是水洗唛条码格式（如"100004-01"）
            const regex = /^(\d+)-(\d+)$/;
            const match = barcode.match(regex);
            
            if (match) {
                return {
                    isClothingBarcode: true,
                    orderNumber: match[1],
                    clothingIndex: parseInt(match[2])
                };
            } else {
                return {
                    isClothingBarcode: false,
                    orderNumber: barcode
                };
            }
        }

        // 更新状态
        function updateStatus(barcode) {
            // 显示加载状态
            showAlert('处理中...', 'info');
            
            // 解析条码
            const barcodeInfo = parseBarcode(barcode);
            
            // 构建请求参数
            let requestData = {
                target_status: '{{ target_status }}'
            };
            
            // 根据条码类型选择API和参数
            let apiUrl = '';
            if (barcodeInfo.isClothingBarcode) {
                apiUrl = '/api/update_clothing_status';
                requestData.order_number = barcodeInfo.orderNumber;
                requestData.clothing_index = barcodeInfo.clothingIndex;
            } else {
                apiUrl = '{{ update_endpoint }}';
                requestData.order_number = barcodeInfo.orderNumber;
            }
            
            $.ajax({
                url: apiUrl,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(response) {
                    if (response.success) {
                        showResult(response);
                        $('#orderInput').val('').focus();
                        fetchHistory(); // 刷新历史记录
                    } else {
                        showAlert(response.error || '操作失败', 'danger');
                    }
                },
                error: function(xhr) {
                    let errorMsg = '系统错误，请稍后再试';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        errorMsg = response.error || errorMsg;
                    } catch (e) {}
                    
                    showAlert(errorMsg, 'danger');
                }
            });
        }

        // 全局变量存储当前订单号
        let currentOrderNumber = null;

        // 显示结果
        function showResult(data) {
            const now = new Date();
            const formattedTime = now.toLocaleString();

            // 存储当前订单号
            currentOrderNumber = data.order_number;

            // 设置订单详情
            $('#orderNumber').text(data.order_number);
            $('#customerName').text(data.customer_name);
            $('#oldStatus').text(data.old_status);
            $('#updateTime').text(formattedTime);

            // 如果是衣物状态更新，显示衣物信息
            if (data.clothing_name) {
                $('#clothingInfo').show();
                $('#clothingName').text(data.clothing_name);
            } else {
                $('#clothingInfo').hide();
            }

            // 显示成功信息
            showAlert('状态已成功更新为"{{ target_status }}"', 'success');

            // 显示结果容器
            $('#resultContainer').show();
            $('#orderDetails').show();

            // 绑定查看订单详情按钮事件
            $('#viewOrderDetailBtn').off('click').on('click', function() {
                if (currentOrderNumber) {
                    // 构建历史页面URL，并传递订单号参数
                    const historyUrl = `/history?order_number=${encodeURIComponent(currentOrderNumber)}`;
                    // 在新标签页中打开历史页面
                    window.open(historyUrl, '_blank');
                }
            });
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertBox = $('#alertBox');
            alertBox.text(message);
            alertBox.removeClass('alert-success alert-danger alert-info');
            alertBox.addClass('alert-' + type);
            
            $('#resultContainer').show();
        }

        // 隐藏结果
        function hideResult() {
            $('#resultContainer').hide();
        }

        // 获取历史记录
        function fetchHistory() {
            $.ajax({
                url: '{{ history_endpoint }}',
                type: 'GET',
                success: function(response) {
                    updateHistoryTable(response.history || []);
                },
                error: function() {
                    console.error('获取历史记录失败');
                }
            });
        }

        // 更新历史表格
        function updateHistoryTable(history) {
            const tbody = $('#historyBody');
            tbody.empty();
            
            history.forEach(function(item) {
                const row = `
                    <tr>
                        <td>${item.order_number}</td>
                        <td>${item.customer_name}</td>
                        <td>${item.clothing_name || '-'}</td>
                        <td>${item.old_status}</td>
                        <td>${item.new_status}</td>
                        <td>${formatDate(item.created_at)}</td>
                    </tr>
                `;
                tbody.append(row);
            });
            
            if (history.length === 0) {
                tbody.append('<tr><td colspan="6" class="text-center">暂无记录</td></tr>');
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleString();
        }
    </script>
</body>
</html> 