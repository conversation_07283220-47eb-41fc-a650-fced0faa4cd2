<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soulweave改衣坊 - 管理后台</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            padding: 20px;
        }
        header {
            background-color: #007BFF;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header-title {
            display: flex;
            align-items: center;
        }
        .header-title h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-info span {
            margin-right: 15px;
        }
        .logout-btn {
            padding: 8px 15px;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        .dashboard {
            display: flex;
            margin-top: 20px;
        }
        .sidebar {
            width: 250px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px 0;
            margin-right: 20px;
        }
        .sidebar-menu {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        .sidebar-menu li {
            padding: 0;
            margin: 0;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            border-left: 4px solid transparent;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: #f5f7fa;
            border-left-color: #007BFF;
            color: #007BFF;
        }
        .main-content {
            flex: 1;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .content-header h2 {
            margin: 0;
            color: #333;
        }
        .search-bar {
            display: flex;
            align-items: center;
        }
        .search-bar input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        .search-bar button {
            padding: 8px 15px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .order-list {
            width: 100%;
            border-collapse: collapse;
        }
        .order-list th, .order-list td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .order-list th {
            background-color: #f5f7fa;
            font-weight: bold;
            color: #333;
        }
        .order-list tbody tr:hover {
            background-color: #f9f9f9;
        }
        .order-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: bold;
            display: inline-block;
        }
        .status-pending {
            background-color: #ffc107;
            color: #212529;
        }
        .status-processing {
            background-color: #17a2b8;
        }
        .status-completed {
            background-color: #28a745;
        }
        .status-cancelled {
            background-color: #dc3545;
        }
        /* 新增状态样式 */
        .status-sorted {
            background-color: #6610f2;
        }
        .status-to-factory {
            background-color: #fd7e14;
        }
        .status-in-factory {
            background-color: #e83e8c;
        }
        .status-factory-sorted {
            background-color: #563d7c;
        }
        .status-out-factory {
            background-color: #0062cc;
        }
        .status-to-branch {
            background-color: #20c997;
        }
        .status-at-branch {
            background-color: #17a2b8;
        }
        .status-on-shelf {
            background-color: #6f42c1;
        }
        .status-delivering {
            background-color: #e83e8c;
        }
        .status-compensated {
            background-color: #adb5bd;
        }
        .status-self-picked {
            background-color: #28a745;
        }
        .status-delivered {
            background-color: #28a745;
        }
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
            font-size: 0.85rem;
        }
        .view-btn {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        .edit-btn {
            background-color: #fff8e6;
            color: #f5a623;
        }
        .delete-btn {
            background-color: #fff1f0;
            color: #f5222d;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .pagination button {
            padding: 8px 15px;
            margin: 0 5px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .pagination button.active {
            background-color: #007BFF;
            color: white;
            border-color: #007BFF;
        }
        .empty-state {
            text-align: center;
            padding: 40px 0;
            color: #999;
        }
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-title">
            <h1>Soulweave改衣坊管理系统</h1>
        </div>
        <div class="user-info">
            <span>欢迎，{{ username }}</span>
            <button class="logout-btn" id="logoutBtn">退出登录</button>
        </div>
    </header>
    
    <div class="container">
        <div class="dashboard">
            <aside class="sidebar">
                <ul class="sidebar-menu">
                    <li><a href="#" class="active" data-section="order-management">订单管理</a></li>
                    <li><a href="#" data-section="customer-management">客户管理</a></li>
                    <li><a href="#" data-section="financial-reports">财务报表</a></li>
                    <li><a href="#" data-section="system-settings">系统设置</a></li>
                </ul>
            </aside>
            
            <main class="main-content">
                <section id="order-management" class="content-section">
                    <div class="content-header">
                        <h2>订单管理</h2>
                        <div class="search-bar">
                            <input type="text" id="orderSearchInput" placeholder="搜索订单号/客户...">
                            <button id="orderSearchBtn">搜索</button>
                        </div>
                    </div>
                    
                    <div id="orderListContainer">
                        <table class="order-list">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>客户名称</th>
                                    <th>联系方式</th>
                                    <th>订单金额</th>
                                    <th>创建时间</th>
                                    <th>订单状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="orderTableBody">
                                <!-- 订单数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                        
                        <div class="pagination" id="orderPagination">
                            <!-- 分页控件将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </section>
                
                <section id="customer-management" class="content-section" style="display: none;">
                    <div class="content-header">
                        <h2>客户管理</h2>
                    </div>
                    <div class="empty-state">
                        <i>👥</i>
                        <p>客户管理功能开发中...</p>
                    </div>
                </section>
                
                <section id="financial-reports" class="content-section" style="display: none;">
                    <div class="content-header">
                        <h2>财务报表</h2>
                    </div>
                    <div class="empty-state">
                        <i>📊</i>
                        <p>财务报表功能开发中...</p>
                    </div>
                </section>
                
                <section id="system-settings" class="content-section" style="display: none;">
                    <div class="content-header">
                        <h2>系统设置</h2>
                    </div>
                    
                    <!-- 订单状态流转管理 -->
                    <div class="settings-card" style="margin-bottom: 20px; background-color: white; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 20px;">
                        <h3 style="margin-top: 0; border-bottom: 1px solid #eee; padding-bottom: 10px;">订单状态流转管理</h3>
                        
                        <div style="margin-bottom: 20px;">
                            <p>本系统支持以下订单状态：</p>
                            <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 15px;">
                                <span class="order-status status-sorted">门店已分拣</span>
                                <span class="order-status status-to-factory">送至工厂中</span>
                                <span class="order-status status-in-factory">入厂</span>
                                <span class="order-status status-factory-sorted">工厂分拣</span>
                                <span class="order-status status-out-factory">出厂</span>
                                <span class="order-status status-to-branch">送至分店中</span>
                                <span class="order-status status-at-branch">已送至分店</span>
                                <span class="order-status status-on-shelf">已上架</span>
                                <span class="order-status status-delivering">配送中</span>
                                <span class="order-status status-self-picked">已自取</span>
                                <span class="order-status status-delivered">已配送</span>
                                <span class="order-status status-cancelled">已取消</span>
                                <span class="order-status status-compensated">已退赔</span>
                            </div>
                            
                            <p>订单状态流转规则：</p>
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                                <thead>
                                    <tr>
                                        <th style="padding: 10px; text-align: left; border-bottom: 1px solid #eee; background-color: #f5f7fa;">当前状态</th>
                                        <th style="padding: 10px; text-align: left; border-bottom: 1px solid #eee; background-color: #f5f7fa;">可流转至</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-sorted">门店已分拣</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-to-factory">送至工厂中</span>
                                            <span class="order-status status-cancelled">已取消</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-to-factory">送至工厂中</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-in-factory">入厂</span>
                                            <span class="order-status status-cancelled">已取消</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-in-factory">入厂</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-factory-sorted">工厂分拣</span>
                                            <span class="order-status status-cancelled">已取消</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-factory-sorted">工厂分拣</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-out-factory">出厂</span>
                                            <span class="order-status status-cancelled">已取消</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-out-factory">出厂</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-to-branch">送至分店中</span>
                                            <span class="order-status status-cancelled">已取消</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-to-branch">送至分店中</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-at-branch">已送至分店</span>
                                            <span class="order-status status-cancelled">已取消</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-at-branch">已送至分店</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-on-shelf">已上架</span>
                                            <span class="order-status status-cancelled">已取消</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-on-shelf">已上架</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-delivering">配送中</span>
                                            <span class="order-status status-self-picked">已自取</span>
                                            <span class="order-status status-cancelled">已取消</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-delivering">配送中</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-delivered">已配送</span>
                                            <span class="order-status status-cancelled">已取消</span>
                                            <span class="order-status status-compensated">已退赔</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-cancelled">已取消</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-compensated">已退赔</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-compensated">已退赔</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span style="font-style: italic; color: #999;">终态，不可再流转</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-self-picked">已自取</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span style="font-style: italic; color: #999;">终态，不可再流转</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span class="order-status status-delivered">已配送</span>
                                        </td>
                                        <td style="padding: 10px; border-bottom: 1px solid #eee;">
                                            <span style="font-style: italic; color: #999;">终态，不可再流转</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <div style="background-color: #fff8e6; padding: 15px; border-radius: 5px; border-left: 4px solid #f5a623;">
                                <h4 style="margin-top: 0; color: #f5a623;">注意事项</h4>
                                <ul style="margin-bottom: 0; padding-left: 20px;">
                                    <li>普通员工只能按照上述流转规则修改订单状态</li>
                                    <li>管理员可以跳过流转规则直接修改订单状态</li>
                                    <li>终态订单的状态不推荐再进行修改</li>
                                    <li>所有状态变更都会记录在系统日志中，便于追踪</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="empty-state">
                        <i>⚙️</i>
                        <p>更多系统设置功能开发中...</p>
                    </div>
                </section>
            </main>
        </div>
    </div>
    
    <!-- 订单详情模态框 -->
    <div id="orderDetailModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: relative; width: 80%; max-width: 800px; margin: 50px auto; background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);">
            <button id="closeModalBtn" style="position: absolute; top: 15px; right: 15px; background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
            <h2>订单详情</h2>
            <div id="orderDetailContent"></div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边菜单切换
            const menuLinks = document.querySelectorAll('.sidebar-menu a');
            const contentSections = document.querySelectorAll('.content-section');
            
            menuLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有active类
                    menuLinks.forEach(item => item.classList.remove('active'));
                    
                    // 为当前点击的链接添加active类
                    this.classList.add('active');
                    
                    // 隐藏所有内容区域
                    contentSections.forEach(section => {
                        section.style.display = 'none';
                    });
                    
                    // 显示当前选中的内容区域
                    const targetSection = this.getAttribute('data-section');
                    document.getElementById(targetSection).style.display = 'block';
                });
            });
            
            // 加载订单数据
            loadOrders();
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                if (confirm('确定要退出登录吗？')) {
                    fetch('/logout', {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.href = '/login';
                        }
                    });
                }
            });
            
            // 关闭订单详情模态框
            document.getElementById('closeModalBtn').addEventListener('click', function() {
                document.getElementById('orderDetailModal').style.display = 'none';
            });
            
            // 搜索订单
            document.getElementById('orderSearchBtn').addEventListener('click', function() {
                const keyword = document.getElementById('orderSearchInput').value.trim();
                loadOrders(1, keyword);
            });
            
            // 按下回车键搜索
            document.getElementById('orderSearchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('orderSearchBtn').click();
                }
            });
        });
        
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let searchKeyword = '';
        
        // 加载订单列表
        function loadOrders(page = 1, keyword = '') {
            currentPage = page;
            searchKeyword = keyword;
            
            // 显示加载状态
            document.getElementById('orderTableBody').innerHTML = '<tr><td colspan="7" style="text-align: center;">加载中...</td></tr>';
            
            // 构建请求URL
            let url = `/api/orders?page=${page}`;
            if (keyword) {
                url += `&search=${encodeURIComponent(keyword)}`;
            }
            
            // 发送请求获取订单数据
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderOrders(data.orders);
                        renderPagination(data.pagination);
                    } else {
                        document.getElementById('orderTableBody').innerHTML = 
                            `<tr><td colspan="7" style="text-align: center;">加载订单失败: ${data.error}</td></tr>`;
                    }
                })
                .catch(error => {
                    console.error('加载订单出错:', error);
                    document.getElementById('orderTableBody').innerHTML = 
                        '<tr><td colspan="7" style="text-align: center;">加载订单时出错，请刷新页面重试</td></tr>';
                });
        }
        
        // 渲染订单列表
        function renderOrders(orders) {
            const tableBody = document.getElementById('orderTableBody');
            
            if (!orders || orders.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无订单数据</td></tr>';
                return;
            }
            
            let html = '';
            orders.forEach(order => {
                html += `
                    <tr>
                        <td>${order.order_number}</td>
                        <td>${order.customer_name}</td>
                        <td>${order.customer_phone}</td>
                        <td>¥${order.total_amount.toFixed(2)}</td>
                        <td>${formatDate(order.created_at)}</td>
                        <td>
                            <span class="order-status status-${getStatusClass(order.status)}">${order.status}</span>
                            <br>
                            <small style="margin-top: 5px; display: inline-block;">
                                <span class="order-status ${order.payment_status === '已付款' ? 'status-completed' : 'status-pending'}" style="font-size: 0.8em; padding: 2px 6px;">
                                    ${order.payment_status}
                                </span>
                            </small>
                        </td>
                        <td>
                            <button class="action-btn view-btn" onclick="viewOrderDetail(${order.id})">查看</button>
                            <button class="action-btn edit-btn" onclick="updateOrderStatus(${order.id})">更新状态</button>
                        </td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        }
        
        // 渲染分页控件
        function renderPagination(pagination) {
            const paginationContainer = document.getElementById('orderPagination');
            totalPages = pagination.total_pages;
            
            let html = '';
            
            // 上一页按钮
            html += `<button ${currentPage === 1 ? 'disabled' : ''} onclick="loadOrders(${currentPage - 1}, '${searchKeyword}')">上一页</button>`;
            
            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                html += `<button class="${currentPage === i ? 'active' : ''}" onclick="loadOrders(${i}, '${searchKeyword}')">${i}</button>`;
            }
            
            // 下一页按钮
            html += `<button ${currentPage === totalPages ? 'disabled' : ''} onclick="loadOrders(${currentPage + 1}, '${searchKeyword}')">下一页</button>`;
            
            paginationContainer.innerHTML = html;
        }
        
        // 查看订单详情
        function viewOrderDetail(orderId) {
            // 显示模态框并设置加载状态
            const modal = document.getElementById('orderDetailModal');
            const contentContainer = document.getElementById('orderDetailContent');
            contentContainer.innerHTML = '<div style="text-align: center; padding: 20px;">加载中...</div>';
            modal.style.display = 'block';
            
            // 加载订单详情
            fetch(`/order_details?id=${orderId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        contentContainer.innerHTML = generateOrderDetailHTML(data.order);
                    } else if (data.error) {
                        contentContainer.innerHTML = `<div style="text-align: center; color: #f5222d;">加载失败: ${data.error}</div>`;
                    } else {
                        // 直接返回订单数据的情况
                        contentContainer.innerHTML = generateOrderDetailHTML(data);
                    }
                })
                .catch(error => {
                    console.error('加载订单详情出错:', error);
                    contentContainer.innerHTML = '<div style="text-align: center; color: #f5222d;">加载订单详情时出错，请重试</div>';
                });
        }
        
        // 更新订单状态
        function updateOrderStatus(orderId) {
            // 获取可选的状态
            const statuses = [
                '门店已分拣', 
                '送至工厂中', 
                '入厂',
                '工厂分拣',
                '出厂',
                '送至分店中', 
                '已送至分店', 
                '已上架', 
                '配送中', 
                '已取消', 
                '已退赔', 
                '已自取', 
                '已配送'
            ];
            
            // 创建状态选择对话框
            let statusHTML = '请选择新的订单状态:<br><br>';
            statuses.forEach(status => {
                statusHTML += `<label style="display: block; margin-bottom: 10px;">
                    <input type="radio" name="status" value="${status}"> ${status}
                </label>`;
            });
            
            // 添加支付状态选择
            statusHTML += '<hr style="margin: 15px 0;"><h3 style="margin-top: 0;">支付状态</h3>';
            statusHTML += `
                <label style="display: block; margin-bottom: 10px;">
                    <input type="radio" name="payment_status" value="已付款"> 已付款
                </label>
                <label style="display: block; margin-bottom: 10px;">
                    <input type="radio" name="payment_status" value="未付款"> 未付款
                </label>
            `;
            
            // 添加备注输入框
            statusHTML += '<hr style="margin: 15px 0;"><h3 style="margin-top: 0;">备注</h3>';
            statusHTML += '<textarea id="statusRemarks" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;" rows="3" placeholder="请输入状态变更备注（可选）"></textarea>';
            
            // 自定义确认对话框，因为JS自带的confirm不支持这么复杂的UI
            const dialogContainer = document.createElement('div');
            dialogContainer.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 2000; display: flex; justify-content: center; align-items: center;';
            
            const dialogBox = document.createElement('div');
            dialogBox.style.cssText = 'background: white; border-radius: 8px; padding: 20px; width: 350px; max-height: 80vh; overflow-y: auto; box-shadow: 0 2px 10px rgba(0,0,0,0.2);';
            
            dialogBox.innerHTML = `
                <h3 style="margin-top: 0;">更新订单状态</h3>
                <div>${statusHTML}</div>
                <div style="text-align: right; margin-top: 20px;">
                    <button id="cancelStatusUpdate" style="margin-right: 10px; padding: 8px 15px; background: #f5f5f5; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                    <button id="confirmStatusUpdate" style="padding: 8px 15px; background: #007BFF; color: white; border: none; border-radius: 4px; cursor: pointer;">确认</button>
                </div>
            `;
            
            dialogContainer.appendChild(dialogBox);
            document.body.appendChild(dialogContainer);
            
            // 取消按钮事件
            document.getElementById('cancelStatusUpdate').addEventListener('click', function() {
                document.body.removeChild(dialogContainer);
            });
            
            // 确认按钮事件
            document.getElementById('confirmStatusUpdate').addEventListener('click', function() {
                const selectedStatus = document.querySelector('input[name="status"]:checked');
                const selectedPaymentStatus = document.querySelector('input[name="payment_status"]:checked');
                const remarks = document.getElementById('statusRemarks').value;
                
                if (!selectedStatus && !selectedPaymentStatus) {
                    alert('请至少选择一个状态');
                    return;
                }
                
                // 构建请求数据
                const requestData = {};
                if (selectedStatus) {
                    requestData.status = selectedStatus.value;
                }
                if (selectedPaymentStatus) {
                    requestData.payment_status = selectedPaymentStatus.value;
                }
                if (remarks) {
                    requestData.remarks = remarks;
                }
                
                // 发送状态更新请求
                fetch(`/update_order_status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        order_id: orderId,
                        ...requestData
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 关闭对话框
                        document.body.removeChild(dialogContainer);
                        // 重新加载订单列表
                        loadOrders(currentPage, searchKeyword);
                        alert('订单状态已更新');
                    } else {
                        alert(`更新失败: ${data.error}`);
                    }
                })
                .catch(error => {
                    console.error('更新订单状态出错:', error);
                    alert('更新订单状态时出错，请重试');
                });
            });
        }
        
        // 生成订单详情HTML
        function generateOrderDetailHTML(order) {
            let html = `
                <div style="padding: 20px; border: 1px solid #eee; border-radius: 8px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; border-bottom: 1px solid #eee; padding-bottom: 15px; margin-bottom: 15px;">
                        <div>
                            <h3 style="margin-top: 0;">基本信息</h3>
                            <p><strong>订单号:</strong> ${order.order_number}</p>
                            <p><strong>创建时间:</strong> ${formatDate(order.created_at)}</p>
                            <p><strong>状态:</strong> <span class="order-status status-${getStatusClass(order.status)}">${order.status}</span></p>
                            <p><strong>支付方式:</strong> ${order.payment_method}</p>
                            <p><strong>支付状态:</strong> <span class="order-status ${order.payment_status === '已付款' ? 'status-completed' : 'status-pending'}">${order.payment_status}</span></p>
                        </div>
                        <div>
                            <h3 style="margin-top: 0;">客户信息</h3>
                            <p><strong>姓名:</strong> ${order.customer_name}</p>
                            <p><strong>电话:</strong> ${order.customer_phone}</p>
                            <p><strong>地址:</strong> ${order.address || '无'}</p>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <h3>订单状态更新</h3>
                        <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 10px;">
                            <button onclick="updateOrderStatus(${order.id})" style="padding: 8px 16px; background-color: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                更新状态
                            </button>
                        </div>
                        
                        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px;">
                            <h4 style="margin-top: 0;">状态变更历史</h4>
                            <div id="statusLogs" style="max-height: 150px; overflow-y: auto;">
                                ${order.status_logs && order.status_logs.length > 0 ? 
                                    `<table style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr>
                                                <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">变更时间</th>
                                                <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">操作人</th>
                                                <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">旧状态</th>
                                                <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">新状态</th>
                                                <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${order.status_logs.map(log => `
                                                <tr>
                                                    <td style="padding: 8px; border-bottom: 1px solid #eee;">${formatDate(log.created_at)}</td>
                                                    <td style="padding: 8px; border-bottom: 1px solid #eee;">${log.changed_by}</td>
                                                    <td style="padding: 8px; border-bottom: 1px solid #eee;">
                                                        <span class="order-status status-${getStatusClass(log.old_status)}" style="font-size: 0.8em; padding: 2px 5px;">${log.old_status}</span>
                                                    </td>
                                                    <td style="padding: 8px; border-bottom: 1px solid #eee;">
                                                        <span class="order-status status-${getStatusClass(log.new_status)}" style="font-size: 0.8em; padding: 2px 5px;">${log.new_status}</span>
                                                    </td>
                                                    <td style="padding: 8px; border-bottom: 1px solid #eee;">${log.remarks || '-'}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>` : 
                                    '<p style="color: #999; font-style: italic;">暂无状态变更记录</p>'
                                }
                            </div>
                        </div>
                    </div>
                    
                    <h3>衣物信息</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr>
                                <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">名称</th>
                                <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">颜色</th>
                                <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">服务类型</th>
                                <th style="text-align: left; padding: 10px; border-bottom: 1px solid #eee;">特殊要求</th>
                                <th style="text-align: right; padding: 10px; border-bottom: 1px solid #eee;">价格</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            order.clothes.forEach(item => {
                html += `
                    <tr>
                        <td style="padding: 10px; border-bottom: 1px solid #eee;">${item.name}</td>
                        <td style="padding: 10px; border-bottom: 1px solid #eee;">${item.color}</td>
                        <td style="padding: 10px; border-bottom: 1px solid #eee;">${(JSON.parse(item.services) || []).join(', ')}</td>
                        <td style="padding: 10px; border-bottom: 1px solid #eee;">${formatRequirements(item.special_requirements)}</td>
                        <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">¥${item.price.toFixed(2)}</td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4" style="text-align: right; padding: 10px; font-weight: bold;">总计:</td>
                                <td style="text-align: right; padding: 10px; font-weight: bold;">¥${order.total_amount.toFixed(2)}</td>
                            </tr>
                            ${order.discount_amount > 0 ? `
                            <tr>
                                <td colspan="4" style="text-align: right; padding: 10px; color: #888;">折扣:</td>
                                <td style="text-align: right; padding: 10px; color: #888;">-¥${order.discount_amount.toFixed(2)}</td>
                            </tr>
                            <tr>
                                <td colspan="4" style="text-align: right; padding: 10px; font-weight: bold; color: #e91e63;">实付金额:</td>
                                <td style="text-align: right; padding: 10px; font-weight: bold; color: #e91e63;">¥${(order.total_amount - order.discount_amount).toFixed(2)}</td>
                            </tr>
                            ` : ''}
                        </tfoot>
                    </table>
                </div>
            `;
            
            return html;
        }
        
        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
        }
        
        // 补零
        function padZero(num) {
            return num < 10 ? `0${num}` : num;
        }
        
        // 获取状态对应的样式类
        function getStatusClass(status) {
            switch (status) {
                case '门店已分拣': return 'sorted';
                case '送至工厂中': return 'to-factory';
                case '入厂': return 'in-factory';
                case '工厂分拣': return 'factory-sorted';
                case '出厂': return 'out-factory';
                case '送至分店中': return 'to-branch';
                case '已送至分店': return 'at-branch';
                case '已上架': return 'on-shelf';
                case '配送中': return 'delivering';
                case '已取消': return 'cancelled';
                case '已退赔': return 'compensated';
                case '已自取': return 'self-picked';
                case '已配送': return 'delivered';
                // 兼容旧状态
                case '待处理': return 'pending';
                case '处理中': return 'processing';
                case '已完成': return 'completed';
                default: return 'pending';
            }
        }
        
        // 格式化特殊要求
        function formatRequirements(requirementsStr) {
            try {
                if (!requirementsStr) return '无';
                
                const requirements = JSON.parse(requirementsStr);
                let result = [];
                
                if (requirements.darn) {
                    result.push(`织补: ${requirements.darn}`);
                }
                
                if (requirements.alter) {
                    result.push(`改衣: ${requirements.alter}`);
                }
                
                return result.length > 0 ? result.join('<br>') : '无';
            } catch (e) {
                return requirementsStr || '无';
            }
        }
    </script>
</body>
</html> 