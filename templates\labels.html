<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水洗唛打印</title>
    <style>
        @media print {
            @page {
                size: 50mm 30mm;
                margin: 0;
            }
            
            body {
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
                font-size: 10px;
            }
            
            .label-item {
                width: 50mm;
                height: 30mm;
                padding: 2mm;
                page-break-after: always;
                box-sizing: border-box;
            }
            
            .label-header {
                text-align: center;
                margin-bottom: 1mm;
            }
            
            .label-header h1 {
                font-size: 12px;
                margin: 0;
                padding: 0;
            }
            
            .label-body {
                font-size: 10px;
                line-height: 1.2;
            }
            
            .label-body p {
                margin: 1mm 0;
            }
            
            .barcode {
                text-align: center;
                margin: 1mm 0;
            }
            
            .barcode img {
                max-width: 100%;
                height: 10mm;
            }
            
            .print-button, .page-break {
                display: none;
            }
        }
        
        /* 非打印样式 */
        body {
            font-family: Aria<PERSON>, sans-serif;
            font-size: 14px;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .labels-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }
        
        .label-item {
            width: 50mm;
            height: 30mm;
            padding: 5mm;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            box-sizing: border-box;
            border: 1px solid #ddd;
        }
        
        .label-header {
            text-align: center;
            margin-bottom: 3mm;
        }
        
        .label-header h1 {
            font-size: 14px;
            margin: 0;
            padding: 0;
        }
        
        .label-body {
            font-size: 12px;
            line-height: 1.3;
        }
        
        .label-body p {
            margin: 2mm 0;
        }
        
        .barcode {
            text-align: center;
            margin: 3mm 0;
        }
        
        .barcode img {
            max-width: 100%;
            height: auto;
        }
        
        .print-button {
            display: block;
            width: 200px;
            margin: 20px auto;
            padding: 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-align: center;
        }
        
        .page-break {
            page-break-after: always;
            height: 0;
            margin: 0;
            padding: 0;
        }
        
        /* 移动端样式 */
        @media screen and (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .labels-container {
                gap: 10px;
            }
            
            .label-item {
                width: 45%;
                height: auto;
                aspect-ratio: 5/3;
                padding: 3mm;
            }
            
            .print-button {
                width: 90%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="labels-container">
        {% for item in clothes %}
        <div class="label-item" id="label-{{ loop.index0 }}">
            <div class="label-header">
                <h1>Soulweave改衣坊</h1>
            </div>
            
            <div class="label-body">
                <p><strong>订单号:</strong> {{ order.order_number }}</p>
                <p><strong>衣物:</strong> {{ item.name }} ({{ item.color }})</p>
                <p><strong>客户:</strong> {{ customer.name }}</p>
            </div>
            
            <div class="barcode">
                <img src="/barcode/{{ order.order_number }}/{{ loop.index0 }}" alt="衣物条码">
            </div>
        </div>
        {% endfor %}
    </div>
    
    {% if is_mobile %}
    <button class="print-button" onclick="window.print()">打印所有水洗唛</button>
    {% endif %}
    
    <script>
        // 添加打印单个标签的功能
        function printSingleLabel(index) {
            const allLabels = document.querySelectorAll('.label-item');
            
            // 隐藏所有标签
            allLabels.forEach(label => {
                label.style.display = 'none';
            });
            
            // 只显示选中的标签
            const selectedLabel = document.getElementById('label-' + index);
            if (selectedLabel) {
                selectedLabel.style.display = 'block';
            }
            
            // 打印
            window.print();
            
            // 恢复显示所有标签
            setTimeout(() => {
                allLabels.forEach(label => {
                    label.style.display = 'block';
                });
            }, 500);
        }
    </script>
</body>
</html>
